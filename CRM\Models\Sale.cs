using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CRM.Models
{
    public class Sale
    {
        [Key]
        public int SaleId { get; set; }

        [Required]
        [StringLength(20)]
        public string SaleNumber { get; set; } = string.Empty;

        [Required]
        public int CustomerId { get; set; }

        [Required]
        public string SalespersonId { get; set; } = string.Empty;

        [Column(TypeName = "decimal(12,2)")]
        public decimal TotalAmount { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal? DiscountAmount { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal FinalAmount { get; set; }

        [StringLength(100)]
        public string? PaymentTerms { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal AnnualRenewalAmount { get; set; }

        public DateTime RenewalDate { get; set; }

        public bool AutoRenewal { get; set; } = false;

        public DateTime SaleDate { get; set; } = DateTime.UtcNow;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(1000)]
        public string? Notes { get; set; }

        public bool IsActive { get; set; } = true;

        // Foreign Keys
        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; } = null!;

        [ForeignKey("SalespersonId")]
        public virtual ApplicationUser Salesperson { get; set; } = null!;

        // Navigation properties
        public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();
        public virtual ICollection<Query> Queries { get; set; } = new List<Query>();
        public virtual ICollection<Job> Jobs { get; set; } = new List<Job>();
        public virtual ICollection<RenewalAlert> RenewalAlerts { get; set; } = new List<RenewalAlert>();

        [NotMapped]
        public string DisplayName => $"Sale #{SaleNumber}";

        [NotMapped]
        public int TotalDevices => SaleItems.Sum(si => si.Quantity);

        [NotMapped]
        public bool IsRenewalDue => RenewalDate <= DateTime.UtcNow.AddDays(90);
    }

    public class SaleItem
    {
        [Key]
        public int SaleItemId { get; set; }

        [Required]
        public int SaleId { get; set; }

        public int? DeviceId { get; set; }

        public int? SIMCardId { get; set; }

        [Required]
        public int Quantity { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal UnitPrice { get; set; }

        [Column(TypeName = "decimal(12,2)")]
        public decimal TotalPrice { get; set; }

        [StringLength(500)]
        public string? SerialNumbers { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Foreign Keys
        [ForeignKey("SaleId")]
        public virtual Sale Sale { get; set; } = null!;

        [ForeignKey("DeviceId")]
        public virtual Device? Device { get; set; }

        [ForeignKey("SIMCardId")]
        public virtual SIMCard? SIMCard { get; set; }

        [NotMapped]
        public string ItemDescription => Device?.DisplayName ?? SIMCard?.DisplayName ?? "Unknown Item";
    }
}
