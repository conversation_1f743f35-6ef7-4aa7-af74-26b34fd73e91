@model CRM.Models.ViewModels.SalesDashboardViewModel
@{
    ViewData["Title"] = "Salesperson Dashboard";
}

<div class="dashboard-header">
    <h1>Welcome back, @Model.SalespersonName!</h1>
    <p class="text-muted">Here's your sales overview</p>
</div>

<div class="row">
    <!-- Key Metrics Cards -->
    <div class="col-md-3 col-sm-6 mb-4">
        <div class="card metric-card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">@Model.TotalSales</h3>
                        <p class="card-text">Total Sales</p>
                    </div>
                    <div class="metric-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 col-sm-6 mb-4">
        <div class="card metric-card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">@Model.TotalRevenue.ToString("C")</h3>
                        <p class="card-text">Total Revenue</p>
                    </div>
                    <div class="metric-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 col-sm-6 mb-4">
        <div class="card metric-card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">@Model.PendingQueries</h3>
                        <p class="card-text">Pending Queries</p>
                    </div>
                    <div class="metric-icon">
                        <i class="fas fa-question-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3 col-sm-6 mb-4">
        <div class="card metric-card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="card-title">@Model.UpcomingRenewals</h3>
                        <p class="card-text">Upcoming Renewals</p>
                    </div>
                    <div class="metric-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Monthly Performance -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Monthly Performance</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <div class="metric-item">
                            <h4>@Model.MonthlySales</h4>
                            <p class="text-muted">This Month Sales</p>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="metric-item">
                            <h4>@Model.MonthlyRevenue.ToString("C")</h4>
                            <p class="text-muted">This Month Revenue</p>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="metric-item">
                            <h4>@Model.AverageOrderValue.ToString("C")</h4>
                            <p class="text-muted">Average Order Value</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="@Url.Action("Create", "Sales")" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Create New Sale
                    </a>
                    <a href="@Url.Action("Index", "Customers")" class="btn btn-outline-primary">
                        <i class="fas fa-users"></i> Manage Customers
                    </a>
                    <a href="@Url.Action("Create", "Queries")" class="btn btn-outline-secondary">
                        <i class="fas fa-question"></i> Submit Query
                    </a>
                    <a href="@Url.Action("Index", "Sales")" class="btn btn-outline-info">
                        <i class="fas fa-list"></i> View All Sales
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Sales -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Recent Sales</h5>
                <a href="@Url.Action("Index", "Sales")" class="btn btn-sm btn-outline-primary">View All</a>
            </div>
            <div class="card-body">
                @if (Model.RecentSales.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Sale #</th>
                                    <th>Customer</th>
                                    <th>Amount</th>
                                    <th>Date</th>
                                    <th>Devices</th>
                                    <th>Renewal</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var sale in Model.RecentSales)
                                {
                                    <tr>
                                        <td>@sale.SaleNumber</td>
                                        <td>@sale.CustomerName</td>
                                        <td>@sale.FinalAmount.ToString("C")</td>
                                        <td>@sale.SaleDate.ToString("MMM dd, yyyy")</td>
                                        <td>@sale.TotalDevices</td>
                                        <td>
                                            <span class="badge @(sale.IsRenewalDue ? "bg-warning" : "bg-success")">
                                                @sale.RenewalDate.ToString("MMM dd, yyyy")
                                            </span>
                                        </td>
                                        <td>
                                            <a href="@Url.Action("Details", "Sales", new { id = sale.SaleId })" 
                                               class="btn btn-sm btn-outline-primary">View</a>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-4">
                        <p class="text-muted">No sales found. <a href="@Url.Action("Create", "Sales")">Create your first sale</a></p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<style>
    .dashboard-header {
        margin-bottom: 2rem;
    }

    .metric-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s;
    }

    .metric-card:hover {
        transform: translateY(-2px);
    }

    .metric-icon {
        font-size: 2rem;
        opacity: 0.8;
    }

    .metric-item {
        text-align: center;
        padding: 1rem 0;
    }

    .metric-item h4 {
        margin-bottom: 0.5rem;
        font-weight: bold;
    }

    .card {
        border: none;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        border-radius: 10px 10px 0 0 !important;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }

    .badge {
        font-size: 0.75rem;
    }
</style>
