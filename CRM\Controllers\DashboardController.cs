using CRM.Attributes;
using CRM.Data;
using CRM.Models;
using CRM.Models.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CRM.Controllers
{
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<DashboardController> _logger;

        public DashboardController(ApplicationDbContext context, ILogger<DashboardController> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            var userRole = User.GetUserRole();
            
            return userRole switch
            {
                UserRole.Salesperson => await SalespersonDashboard(),
                UserRole.OperationsStaff => await OperationsDashboard(),
                UserRole.Technician => await TechnicianDashboard(),
                UserRole.Management => await ManagementDashboard(),
                _ => View("Error")
            };
        }

        private async Task<IActionResult> SalespersonDashboard()
        {
            var userId = User.GetUserId()!;
            var currentDate = DateTime.UtcNow;
            var startOfMonth = new DateTime(currentDate.Year, currentDate.Month, 1);

            var dashboard = new SalesDashboardViewModel
            {
                SalespersonName = User.GetUserName() ?? "Unknown",
                TotalSales = await _context.Sales
                    .Where(s => s.SalespersonId == userId && s.IsActive)
                    .CountAsync(),
                
                TotalRevenue = await _context.Sales
                    .Where(s => s.SalespersonId == userId && s.IsActive)
                    .SumAsync(s => s.FinalAmount),
                
                PendingQueries = await _context.Queries
                    .Where(q => q.CreatedByUserId == userId && 
                               (q.Status == QueryStatus.Pending || q.Status == QueryStatus.UnderReview))
                    .CountAsync(),
                
                CompletedInstallations = await _context.Jobs
                    .Where(j => j.Sale.SalespersonId == userId && j.Status == JobStatus.Completed)
                    .CountAsync(),
                
                UpcomingRenewals = await _context.Sales
                    .Where(s => s.SalespersonId == userId && s.IsActive &&
                               s.RenewalDate <= currentDate.AddDays(90))
                    .CountAsync(),
                
                MonthlyRevenue = await _context.Sales
                    .Where(s => s.SalespersonId == userId && s.IsActive &&
                               s.SaleDate >= startOfMonth)
                    .SumAsync(s => s.FinalAmount),
                
                MonthlySales = await _context.Sales
                    .Where(s => s.SalespersonId == userId && s.IsActive &&
                               s.SaleDate >= startOfMonth)
                    .CountAsync()
            };

            // Calculate average order value
            if (dashboard.TotalSales > 0)
            {
                dashboard.AverageOrderValue = dashboard.TotalRevenue / dashboard.TotalSales;
            }

            // Get recent sales
            dashboard.RecentSales = await _context.Sales
                .Where(s => s.SalespersonId == userId && s.IsActive)
                .Include(s => s.Customer)
                .Include(s => s.SaleItems)
                .OrderByDescending(s => s.SaleDate)
                .Take(10)
                .Select(s => new SaleSummaryViewModel
                {
                    SaleId = s.SaleId,
                    SaleNumber = s.SaleNumber,
                    CustomerName = s.Customer.FullName,
                    FinalAmount = s.FinalAmount,
                    SaleDate = s.SaleDate,
                    TotalDevices = s.SaleItems.Sum(si => si.Quantity),
                    RenewalDate = s.RenewalDate,
                    IsRenewalDue = s.RenewalDate <= currentDate.AddDays(90)
                })
                .ToListAsync();

            // Get monthly performance for the last 12 months
            dashboard.MonthlyPerformance = await GetMonthlyPerformance(userId, 12);

            return View("SalespersonDashboard", dashboard);
        }

        private async Task<IActionResult> OperationsDashboard()
        {
            var currentDate = DateTime.UtcNow;

            var dashboard = new QueryDashboardViewModel
            {
                TotalQueries = await _context.Queries.CountAsync(),
                PendingQueries = await _context.Queries
                    .Where(q => q.Status == QueryStatus.Pending || q.Status == QueryStatus.UnderReview)
                    .CountAsync(),
                OverdueQueries = await _context.Queries
                    .Where(q => q.DueDate.HasValue && q.DueDate.Value < currentDate &&
                               q.Status != QueryStatus.Approved && q.Status != QueryStatus.Rejected)
                    .CountAsync(),
                ProcessedToday = await _context.Queries
                    .Where(q => q.ProcessedAt.HasValue && q.ProcessedAt.Value.Date == currentDate.Date)
                    .CountAsync()
            };

            // Calculate average processing time
            var processedQueries = await _context.Queries
                .Where(q => q.ProcessedAt.HasValue)
                .Select(q => new { q.CreatedAt, q.ProcessedAt })
                .ToListAsync();

            if (processedQueries.Any())
            {
                dashboard.AverageProcessingTime = processedQueries
                    .Average(q => (q.ProcessedAt!.Value - q.CreatedAt).TotalHours);
            }

            // Get recent queries
            dashboard.RecentQueries = await _context.Queries
                .Include(q => q.Sale)
                .ThenInclude(s => s.Customer)
                .Include(q => q.CreatedByUser)
                .Include(q => q.ProcessedByUser)
                .OrderByDescending(q => q.CreatedAt)
                .Take(10)
                .Select(q => new QuerySummaryViewModel
                {
                    QueryId = q.QueryId,
                    QueryNumber = q.QueryNumber,
                    Subject = q.Subject,
                    Priority = q.Priority,
                    Status = q.Status,
                    CreatedByUserName = q.CreatedByUser.FullName,
                    ProcessedByUserName = q.ProcessedByUser != null ? q.ProcessedByUser.FullName : null,
                    CreatedAt = q.CreatedAt,
                    ProcessedAt = q.ProcessedAt,
                    DueDate = q.DueDate,
                    IsOverdue = q.DueDate.HasValue && q.DueDate.Value < currentDate &&
                               q.Status != QueryStatus.Approved && q.Status != QueryStatus.Rejected,
                    SaleNumber = q.Sale.SaleNumber,
                    CustomerName = q.Sale.Customer.FullName
                })
                .ToListAsync();

            return View("OperationsDashboard", dashboard);
        }

        private async Task<IActionResult> TechnicianDashboard()
        {
            var userId = User.GetUserId()!;
            var currentDate = DateTime.UtcNow;

            var jobs = await _context.Jobs
                .Where(j => j.AssignedToUserId == userId)
                .Include(j => j.Sale)
                .ThenInclude(s => s.Customer)
                .OrderBy(j => j.ScheduledDate)
                .ToListAsync();

            var model = new
            {
                TotalJobs = jobs.Count,
                ScheduledJobs = jobs.Count(j => j.Status == JobStatus.Scheduled),
                InProgressJobs = jobs.Count(j => j.Status == JobStatus.InProgress),
                CompletedJobs = jobs.Count(j => j.Status == JobStatus.Completed),
                OverdueJobs = jobs.Count(j => j.ScheduledDate < currentDate && 
                                            j.Status != JobStatus.Completed && 
                                            j.Status != JobStatus.Cancelled),
                TodaysJobs = jobs.Where(j => j.ScheduledDate.Date == currentDate.Date).ToList(),
                UpcomingJobs = jobs.Where(j => j.ScheduledDate.Date > currentDate.Date && 
                                             j.Status == JobStatus.Scheduled)
                                  .Take(10).ToList()
            };

            return View("TechnicianDashboard", model);
        }

        private async Task<IActionResult> ManagementDashboard()
        {
            var currentDate = DateTime.UtcNow;
            var startOfMonth = new DateTime(currentDate.Year, currentDate.Month, 1);

            var model = new
            {
                TotalSales = await _context.Sales.Where(s => s.IsActive).CountAsync(),
                MonthlyRevenue = await _context.Sales
                    .Where(s => s.IsActive && s.SaleDate >= startOfMonth)
                    .SumAsync(s => s.FinalAmount),
                TotalCustomers = await _context.Customers.Where(c => c.IsActive).CountAsync(),
                PendingQueries = await _context.Queries
                    .Where(q => q.Status == QueryStatus.Pending || q.Status == QueryStatus.UnderReview)
                    .CountAsync(),
                ActiveJobs = await _context.Jobs
                    .Where(j => j.Status == JobStatus.Scheduled || j.Status == JobStatus.InProgress)
                    .CountAsync(),
                LowStockItems = await _context.InventoryItems
                    .GroupBy(i => new { i.DeviceId, i.SIMCardId })
                    .Where(g => g.Count(i => i.Status == InventoryStatus.Available) < 10)
                    .CountAsync(),
                UpcomingRenewals = await _context.Sales
                    .Where(s => s.IsActive && s.RenewalDate <= currentDate.AddDays(90))
                    .CountAsync()
            };

            return View("ManagementDashboard", model);
        }

        private async Task<List<MonthlyPerformanceViewModel>> GetMonthlyPerformance(string userId, int months)
        {
            var endDate = DateTime.UtcNow;
            var startDate = endDate.AddMonths(-months);

            var sales = await _context.Sales
                .Where(s => s.SalespersonId == userId && s.IsActive &&
                           s.SaleDate >= startDate && s.SaleDate <= endDate)
                .GroupBy(s => new { s.SaleDate.Year, s.SaleDate.Month })
                .Select(g => new MonthlyPerformanceViewModel
                {
                    Month = $"{g.Key.Year}-{g.Key.Month:D2}",
                    Revenue = g.Sum(s => s.FinalAmount),
                    SalesCount = g.Count()
                })
                .OrderBy(m => m.Month)
                .ToListAsync();

            return sales;
        }
    }
}
