using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CRM.Models
{
    public class Notification
    {
        [Key]
        public int NotificationId { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public NotificationType Type { get; set; }

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        [StringLength(1000)]
        public string Message { get; set; } = string.Empty;

        public bool IsRead { get; set; } = false;

        public bool IsEmailSent { get; set; } = false;

        public bool IsSMSSent { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? ReadAt { get; set; }

        public DateTime? EmailSentAt { get; set; }

        public DateTime? SMSSentAt { get; set; }

        [StringLength(500)]
        public string? ActionUrl { get; set; }

        [StringLength(1000)]
        public string? AdditionalData { get; set; } // JSON data for specific notification types

        // Foreign Keys
        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; } = null!;

        [NotMapped]
        public string TimeAgo
        {
            get
            {
                var timeSpan = DateTime.UtcNow - CreatedAt;
                if (timeSpan.TotalMinutes < 1) return "Just now";
                if (timeSpan.TotalMinutes < 60) return $"{(int)timeSpan.TotalMinutes} minutes ago";
                if (timeSpan.TotalHours < 24) return $"{(int)timeSpan.TotalHours} hours ago";
                if (timeSpan.TotalDays < 30) return $"{(int)timeSpan.TotalDays} days ago";
                return CreatedAt.ToString("MMM dd, yyyy");
            }
        }
    }

    public class RenewalAlert
    {
        [Key]
        public int RenewalAlertId { get; set; }

        [Required]
        public int SaleId { get; set; }

        [Required]
        public int DaysBeforeRenewal { get; set; } // 90, 60, 30

        public bool IsProcessed { get; set; } = false;

        public DateTime AlertDate { get; set; } = DateTime.UtcNow;

        public DateTime? ProcessedAt { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Foreign Keys
        [ForeignKey("SaleId")]
        public virtual Sale Sale { get; set; } = null!;

        [NotMapped]
        public string AlertDescription => $"{DaysBeforeRenewal} days before renewal for {Sale?.DisplayName}";
    }

    public class InventoryAlert
    {
        [Key]
        public int InventoryAlertId { get; set; }

        public int? DeviceId { get; set; }

        public int? SIMCardId { get; set; }

        [Required]
        public int CurrentStock { get; set; }

        [Required]
        public int ThresholdLevel { get; set; }

        public bool IsProcessed { get; set; } = false;

        public DateTime AlertDate { get; set; } = DateTime.UtcNow;

        public DateTime? ProcessedAt { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Foreign Keys
        [ForeignKey("DeviceId")]
        public virtual Device? Device { get; set; }

        [ForeignKey("SIMCardId")]
        public virtual SIMCard? SIMCard { get; set; }

        [NotMapped]
        public string ItemName => Device?.DisplayName ?? SIMCard?.DisplayName ?? "Unknown Item";

        [NotMapped]
        public string AlertDescription => $"Low stock alert: {ItemName} ({CurrentStock} remaining, threshold: {ThresholdLevel})";
    }

    public class AuditLog
    {
        [Key]
        public int AuditLogId { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Action { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string EntityType { get; set; } = string.Empty;

        public int? EntityId { get; set; }

        [StringLength(2000)]
        public string? OldValues { get; set; } // JSON

        [StringLength(2000)]
        public string? NewValues { get; set; } // JSON

        [StringLength(500)]
        public string? Description { get; set; }

        [StringLength(45)]
        public string? IPAddress { get; set; }

        [StringLength(500)]
        public string? UserAgent { get; set; }

        public DateTime Timestamp { get; set; } = DateTime.UtcNow;

        // Foreign Keys
        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; } = null!;

        [NotMapped]
        public string ActionDescription => $"{User?.FullName} {Action} {EntityType}" + (EntityId.HasValue ? $" (ID: {EntityId})" : "");
    }
}
