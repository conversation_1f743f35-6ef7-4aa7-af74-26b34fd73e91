using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CRM.Models
{
    public class Customer
    {
        [Key]
        public int CustomerId { get; set; }

        [Required]
        [StringLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string LastName { get; set; } = string.Empty;

        [StringLength(100)]
        public string? CompanyName { get; set; }

        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; } = string.Empty;

        [Required]
        [Phone]
        [StringLength(20)]
        public string PrimaryPhone { get; set; } = string.Empty;

        [Phone]
        [StringLength(20)]
        public string? SecondaryPhone { get; set; }

        // Billing Address
        [Required]
        [StringLength(500)]
        public string BillingAddress { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string BillingCity { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string BillingState { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string BillingZipCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string BillingCountry { get; set; } = string.Empty;

        // Installation Address
        [Required]
        [StringLength(500)]
        public string InstallationAddress { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string InstallationCity { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string InstallationState { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string InstallationZipCode { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string InstallationCountry { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Navigation properties
        public virtual ICollection<Sale> Sales { get; set; } = new List<Sale>();

        [NotMapped]
        public string FullName => $"{FirstName} {LastName}";

        [NotMapped]
        public string DisplayName => !string.IsNullOrEmpty(CompanyName) 
            ? $"{FullName} ({CompanyName})" 
            : FullName;

        [NotMapped]
        public string FullBillingAddress => $"{BillingAddress}, {BillingCity}, {BillingState} {BillingZipCode}, {BillingCountry}";

        [NotMapped]
        public string FullInstallationAddress => $"{InstallationAddress}, {InstallationCity}, {InstallationState} {InstallationZipCode}, {InstallationCountry}";
    }
}
