@model CRM.Models.ViewModels.LoginViewModel
@{
    ViewData["Title"] = "Login";
    Layout = "_LayoutAuth";
}

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h2>CRM System</h2>
            <p>Sign in to your account</p>
        </div>

        <form asp-action="Login" method="post" class="login-form">
            <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>
            
            <input type="hidden" asp-for="ReturnUrl" />

            <div class="form-group">
                <label asp-for="Email" class="form-label">Email Address</label>
                <input asp-for="Email" class="form-control" placeholder="Enter your email" />
                <span asp-validation-for="Email" class="text-danger"></span>
            </div>

            <div class="form-group">
                <label asp-for="Password" class="form-label">Password</label>
                <input asp-for="Password" class="form-control" placeholder="Enter your password" />
                <span asp-validation-for="Password" class="text-danger"></span>
            </div>

            <div class="form-group form-check">
                <input asp-for="RememberMe" class="form-check-input" />
                <label asp-for="RememberMe" class="form-check-label">Remember me</label>
            </div>

            <button type="submit" class="btn btn-primary btn-block">Sign In</button>

            <div class="login-links">
                <a asp-action="ForgotPassword">Forgot your password?</a>
                <span class="separator">|</span>
                <a asp-action="Register">Create new account</a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}

<style>
    .login-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .login-card {
        background: white;
        padding: 2rem;
        border-radius: 10px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        width: 100%;
        max-width: 400px;
    }

    .login-header {
        text-align: center;
        margin-bottom: 2rem;
    }

    .login-header h2 {
        color: #333;
        margin-bottom: 0.5rem;
    }

    .login-header p {
        color: #666;
        margin: 0;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-label {
        font-weight: 500;
        color: #333;
        margin-bottom: 0.5rem;
        display: block;
    }

    .form-control {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-size: 1rem;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 0.75rem;
        border-radius: 5px;
        color: white;
        font-weight: 500;
        cursor: pointer;
        transition: transform 0.2s;
    }

    .btn-primary:hover {
        transform: translateY(-1px);
    }

    .btn-block {
        width: 100%;
    }

    .login-links {
        text-align: center;
        margin-top: 1.5rem;
    }

    .login-links a {
        color: #667eea;
        text-decoration: none;
    }

    .login-links a:hover {
        text-decoration: underline;
    }

    .separator {
        margin: 0 0.5rem;
        color: #ccc;
    }

    .alert-danger {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
        padding: 0.75rem;
        border-radius: 5px;
        margin-bottom: 1rem;
    }

    .text-danger {
        color: #dc3545;
        font-size: 0.875rem;
    }
</style>
