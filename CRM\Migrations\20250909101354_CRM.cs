﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CRM.Migrations
{
    /// <inheritdoc />
    public partial class CRM : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Devices",
                keyColumn: "DeviceId",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 9, 9, 10, 13, 54, 488, DateTimeKind.Utc).AddTicks(4920), new DateTime(2025, 9, 9, 10, 13, 54, 488, DateTimeKind.Utc).AddTicks(4921) });

            migrationBuilder.UpdateData(
                table: "Devices",
                keyColumn: "DeviceId",
                keyValue: 2,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 9, 9, 10, 13, 54, 488, DateTimeKind.Utc).AddTicks(4923), new DateTime(2025, 9, 9, 10, 13, 54, 488, DateTimeKind.Utc).AddTicks(4923) });

            migrationBuilder.UpdateData(
                table: "SIMCards",
                keyColumn: "SIMCardId",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 9, 9, 10, 13, 54, 488, DateTimeKind.Utc).AddTicks(4945), new DateTime(2025, 9, 9, 10, 13, 54, 488, DateTimeKind.Utc).AddTicks(4946) });

            migrationBuilder.UpdateData(
                table: "SIMCards",
                keyColumn: "SIMCardId",
                keyValue: 2,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 9, 9, 10, 13, 54, 488, DateTimeKind.Utc).AddTicks(4948), new DateTime(2025, 9, 9, 10, 13, 54, 488, DateTimeKind.Utc).AddTicks(4948) });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Devices",
                keyColumn: "DeviceId",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 9, 9, 10, 3, 28, 628, DateTimeKind.Utc).AddTicks(5227), new DateTime(2025, 9, 9, 10, 3, 28, 628, DateTimeKind.Utc).AddTicks(5227) });

            migrationBuilder.UpdateData(
                table: "Devices",
                keyColumn: "DeviceId",
                keyValue: 2,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 9, 9, 10, 3, 28, 628, DateTimeKind.Utc).AddTicks(5230), new DateTime(2025, 9, 9, 10, 3, 28, 628, DateTimeKind.Utc).AddTicks(5230) });

            migrationBuilder.UpdateData(
                table: "SIMCards",
                keyColumn: "SIMCardId",
                keyValue: 1,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 9, 9, 10, 3, 28, 628, DateTimeKind.Utc).AddTicks(5297), new DateTime(2025, 9, 9, 10, 3, 28, 628, DateTimeKind.Utc).AddTicks(5298) });

            migrationBuilder.UpdateData(
                table: "SIMCards",
                keyColumn: "SIMCardId",
                keyValue: 2,
                columns: new[] { "CreatedAt", "UpdatedAt" },
                values: new object[] { new DateTime(2025, 9, 9, 10, 3, 28, 628, DateTimeKind.Utc).AddTicks(5300), new DateTime(2025, 9, 9, 10, 3, 28, 628, DateTimeKind.Utc).AddTicks(5300) });
        }
    }
}
