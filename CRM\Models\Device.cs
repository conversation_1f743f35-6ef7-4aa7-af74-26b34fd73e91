using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CRM.Models
{
    public class Device
    {
        [Key]
        public int DeviceId { get; set; }

        [Required]
        [StringLength(100)]
        public string DeviceName { get; set; } = string.Empty;

        [Required]
        public DeviceType DeviceType { get; set; }

        [Required]
        [StringLength(100)]
        public string Model { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string Manufacturer { get; set; } = string.Empty;

        [StringLength(1000)]
        public string? Description { get; set; }

        [StringLength(1000)]
        public string? Specifications { get; set; }

        [Column(TypeName = "decimal(10,2)")]
        public decimal UnitPrice { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ICollection<InventoryItem> InventoryItems { get; set; } = new List<InventoryItem>();
        public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();

        [NotMapped]
        public string DisplayName => $"{DeviceName} ({Model})";

        [NotMapped]
        public int TotalStock => InventoryItems.Count;

        [NotMapped]
        public int AvailableStock => InventoryItems.Count(i => i.Status == InventoryStatus.Available);

        [NotMapped]
        public int AssignedStock => InventoryItems.Count(i => i.Status == InventoryStatus.Assigned);
    }

    public class SIMCard
    {
        [Key]
        public int SIMCardId { get; set; }

        [Required]
        [StringLength(50)]
        public string SIMNumber { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string ICCID { get; set; } = string.Empty;

        [StringLength(100)]
        public string? Carrier { get; set; }

        [StringLength(50)]
        public string? PlanType { get; set; }

        [Column(TypeName = "decimal(8,2)")]
        public decimal? MonthlyFee { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Navigation properties
        public virtual ICollection<InventoryItem> InventoryItems { get; set; } = new List<InventoryItem>();
        public virtual ICollection<SaleItem> SaleItems { get; set; } = new List<SaleItem>();

        [NotMapped]
        public string DisplayName => $"{SIMNumber} ({Carrier})";
    }
}
