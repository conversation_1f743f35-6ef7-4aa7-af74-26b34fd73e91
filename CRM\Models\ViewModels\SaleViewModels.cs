using System.ComponentModel.DataAnnotations;

namespace CRM.Models.ViewModels
{
    public class CreateSaleViewModel
    {
        // Customer Information
        [Required]
        [Display(Name = "First Name")]
        public string CustomerFirstName { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Last Name")]
        public string CustomerLastName { get; set; } = string.Empty;

        [Display(Name = "Company Name")]
        public string? CustomerCompanyName { get; set; }

        [Required]
        [EmailAddress]
        [Display(Name = "Email")]
        public string CustomerEmail { get; set; } = string.Empty;

        [Required]
        [Phone]
        [Display(Name = "Primary Phone")]
        public string CustomerPrimaryPhone { get; set; } = string.Empty;

        [Phone]
        [Display(Name = "Secondary Phone")]
        public string? CustomerSecondaryPhone { get; set; }

        // Billing Address
        [Required]
        [Display(Name = "Billing Address")]
        public string BillingAddress { get; set; } = string.Empty;

        [Required]
        [Display(Name = "City")]
        public string BillingCity { get; set; } = string.Empty;

        [Required]
        [Display(Name = "State")]
        public string BillingState { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Zip Code")]
        public string BillingZipCode { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Country")]
        public string BillingCountry { get; set; } = string.Empty;

        // Installation Address
        [Required]
        [Display(Name = "Installation Address")]
        public string InstallationAddress { get; set; } = string.Empty;

        [Required]
        [Display(Name = "City")]
        public string InstallationCity { get; set; } = string.Empty;

        [Required]
        [Display(Name = "State")]
        public string InstallationState { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Zip Code")]
        public string InstallationZipCode { get; set; } = string.Empty;

        [Required]
        [Display(Name = "Country")]
        public string InstallationCountry { get; set; } = string.Empty;

        // Sale Items
        public List<SaleItemViewModel> SaleItems { get; set; } = new List<SaleItemViewModel>();

        // Pricing
        [Display(Name = "Discount Amount")]
        [Range(0, double.MaxValue, ErrorMessage = "Discount amount must be positive")]
        public decimal? DiscountAmount { get; set; }

        [Display(Name = "Payment Terms")]
        public string? PaymentTerms { get; set; }

        // Renewal Information
        [Required]
        [Display(Name = "Annual Renewal Amount")]
        [Range(0.01, double.MaxValue, ErrorMessage = "Annual renewal amount must be greater than 0")]
        public decimal AnnualRenewalAmount { get; set; }

        [Required]
        [Display(Name = "Renewal Date")]
        [DataType(DataType.Date)]
        public DateTime RenewalDate { get; set; } = DateTime.UtcNow.AddYears(1);

        [Display(Name = "Auto Renewal")]
        public bool AutoRenewal { get; set; }

        [Display(Name = "Notes")]
        public string? Notes { get; set; }

        // Calculated Properties
        public decimal TotalAmount => SaleItems.Sum(si => si.TotalPrice);
        public decimal FinalAmount => TotalAmount - (DiscountAmount ?? 0);
    }

    public class SaleItemViewModel
    {
        public int? DeviceId { get; set; }
        public int? SIMCardId { get; set; }

        [Required]
        [Range(1, int.MaxValue, ErrorMessage = "Quantity must be at least 1")]
        public int Quantity { get; set; } = 1;

        [Required]
        [Range(0.01, double.MaxValue, ErrorMessage = "Unit price must be greater than 0")]
        [Display(Name = "Unit Price")]
        public decimal UnitPrice { get; set; }

        [Display(Name = "Serial Numbers")]
        public string? SerialNumbers { get; set; }

        [Display(Name = "Notes")]
        public string? Notes { get; set; }

        // Calculated Properties
        public decimal TotalPrice => Quantity * UnitPrice;

        // Display Properties
        public string? DeviceName { get; set; }
        public string? SIMCardName { get; set; }
        public string ItemName => DeviceName ?? SIMCardName ?? "Unknown Item";
    }

    public class SalesDashboardViewModel
    {
        public string SalespersonName { get; set; } = string.Empty;
        public int TotalSales { get; set; }
        public decimal TotalRevenue { get; set; }
        public int PendingQueries { get; set; }
        public int CompletedInstallations { get; set; }
        public int UpcomingRenewals { get; set; }

        // Recent Sales
        public List<SaleSummaryViewModel> RecentSales { get; set; } = new List<SaleSummaryViewModel>();

        // Performance Metrics
        public decimal MonthlyRevenue { get; set; }
        public int MonthlySales { get; set; }
        public decimal AverageOrderValue { get; set; }

        // Charts Data
        public List<MonthlyPerformanceViewModel> MonthlyPerformance { get; set; } = new List<MonthlyPerformanceViewModel>();
    }

    public class SaleSummaryViewModel
    {
        public int SaleId { get; set; }
        public string SaleNumber { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public decimal FinalAmount { get; set; }
        public DateTime SaleDate { get; set; }
        public int TotalDevices { get; set; }
        public DateTime RenewalDate { get; set; }
        public bool IsRenewalDue { get; set; }
    }

    public class MonthlyPerformanceViewModel
    {
        public string Month { get; set; } = string.Empty;
        public decimal Revenue { get; set; }
        public int SalesCount { get; set; }
    }
}
