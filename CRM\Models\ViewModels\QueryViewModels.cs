using System.ComponentModel.DataAnnotations;

namespace CRM.Models.ViewModels
{
    public class CreateQueryViewModel
    {
        [Required]
        [Display(Name = "Sale")]
        public int SaleId { get; set; }

        [Required]
        [Display(Name = "Priority")]
        public Priority Priority { get; set; } = Priority.Medium;

        [Required]
        [StringLength(200)]
        [Display(Name = "Subject")]
        public string Subject { get; set; } = string.Empty;

        [Required]
        [StringLength(2000)]
        [Display(Name = "Description")]
        public string Description { get; set; } = string.Empty;

        [Display(Name = "Due Date")]
        [DataType(DataType.Date)]
        public DateTime? DueDate { get; set; }

        // For dropdown population
        public List<SaleSummaryViewModel> AvailableSales { get; set; } = new List<SaleSummaryViewModel>();
    }

    public class ProcessQueryViewModel
    {
        public int QueryId { get; set; }
        public string QueryNumber { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Priority Priority { get; set; }
        public QueryStatus CurrentStatus { get; set; }
        public string CreatedByUserName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? DueDate { get; set; }

        // Sale Information
        public string SaleNumber { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;

        // Processing Fields
        [Required]
        [Display(Name = "Status")]
        public QueryStatus NewStatus { get; set; }

        [StringLength(2000)]
        [Display(Name = "Response")]
        public string? Response { get; set; }

        [StringLength(2000)]
        [Display(Name = "Internal Notes")]
        public string? InternalNotes { get; set; }

        // Comments
        public List<QueryCommentViewModel> Comments { get; set; } = new List<QueryCommentViewModel>();

        [StringLength(2000)]
        [Display(Name = "Add Comment")]
        public string? NewComment { get; set; }

        [Display(Name = "Internal Comment")]
        public bool IsInternalComment { get; set; }
    }

    public class QueryCommentViewModel
    {
        public int CommentId { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string Comment { get; set; } = string.Empty;
        public bool IsInternal { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class QueryListViewModel
    {
        public List<QuerySummaryViewModel> Queries { get; set; } = new List<QuerySummaryViewModel>();
        public QueryFilterViewModel Filter { get; set; } = new QueryFilterViewModel();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
    }

    public class QuerySummaryViewModel
    {
        public int QueryId { get; set; }
        public string QueryNumber { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public Priority Priority { get; set; }
        public QueryStatus Status { get; set; }
        public string CreatedByUserName { get; set; } = string.Empty;
        public string? ProcessedByUserName { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? ProcessedAt { get; set; }
        public DateTime? DueDate { get; set; }
        public bool IsOverdue { get; set; }
        public string SaleNumber { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
    }

    public class QueryFilterViewModel
    {
        [Display(Name = "Status")]
        public QueryStatus? Status { get; set; }

        [Display(Name = "Priority")]
        public Priority? Priority { get; set; }

        [Display(Name = "Created By")]
        public string? CreatedByUserId { get; set; }

        [Display(Name = "Processed By")]
        public string? ProcessedByUserId { get; set; }

        [Display(Name = "From Date")]
        [DataType(DataType.Date)]
        public DateTime? FromDate { get; set; }

        [Display(Name = "To Date")]
        [DataType(DataType.Date)]
        public DateTime? ToDate { get; set; }

        [Display(Name = "Search")]
        public string? SearchTerm { get; set; }

        [Display(Name = "Show Overdue Only")]
        public bool ShowOverdueOnly { get; set; }
    }

    public class QueryDashboardViewModel
    {
        public int TotalQueries { get; set; }
        public int PendingQueries { get; set; }
        public int OverdueQueries { get; set; }
        public int ProcessedToday { get; set; }
        public double AverageProcessingTime { get; set; }

        public List<QuerySummaryViewModel> RecentQueries { get; set; } = new List<QuerySummaryViewModel>();
        public List<QueryStatusCountViewModel> StatusCounts { get; set; } = new List<QueryStatusCountViewModel>();
        public List<QueryPriorityCountViewModel> PriorityCounts { get; set; } = new List<QueryPriorityCountViewModel>();
    }

    public class QueryStatusCountViewModel
    {
        public QueryStatus Status { get; set; }
        public int Count { get; set; }
        public string StatusName { get; set; } = string.Empty;
    }

    public class QueryPriorityCountViewModel
    {
        public Priority Priority { get; set; }
        public int Count { get; set; }
        public string PriorityName { get; set; } = string.Empty;
    }
}
