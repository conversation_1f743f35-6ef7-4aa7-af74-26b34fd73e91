﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - CRM System</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/CRM.styles.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
</head>
<body>
    @if (User.Identity?.IsAuthenticated == true)
    {
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="@Url.Action("Index", "Dashboard")">
                    <i class="fas fa-chart-line"></i> CRM System
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="@Url.Action("Index", "Dashboard")">
                                <i class="fas fa-tachometer-alt"></i> Dashboard
                            </a>
                        </li>

                        @if (User.HasPermission("Sales.View"))
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-shopping-cart"></i> Sales
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="@Url.Action("Index", "Sales")">All Sales</a></li>
                                    @if (User.HasPermission("Sales.Create"))
                                    {
                                        <li><a class="dropdown-item" href="@Url.Action("Create", "Sales")">Create Sale</a></li>
                                    }
                                </ul>
                            </li>
                        }

                        @if (User.HasPermission("Customers.View"))
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-users"></i> Customers
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="@Url.Action("Index", "Customers")">All Customers</a></li>
                                    @if (User.HasPermission("Customers.Create"))
                                    {
                                        <li><a class="dropdown-item" href="@Url.Action("Create", "Customers")">Add Customer</a></li>
                                    }
                                </ul>
                            </li>
                        }

                        @if (User.HasPermission("Queries.View"))
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-question-circle"></i> Queries
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="@Url.Action("Index", "Queries")">All Queries</a></li>
                                    @if (User.HasPermission("Queries.Create"))
                                    {
                                        <li><a class="dropdown-item" href="@Url.Action("Create", "Queries")">Submit Query</a></li>
                                    }
                                </ul>
                            </li>
                        }

                        @if (User.HasPermission("Inventory.View"))
                        {
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-boxes"></i> Inventory
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="@Url.Action("Index", "Inventory")">All Items</a></li>
                                    <li><a class="dropdown-item" href="@Url.Action("Devices", "Inventory")">Devices</a></li>
                                    <li><a class="dropdown-item" href="@Url.Action("SIMCards", "Inventory")">SIM Cards</a></li>
                                </ul>
                            </li>
                        }

                        @if (User.HasPermission("Jobs.View"))
                        {
                            <li class="nav-item">
                                <a class="nav-link" href="@Url.Action("Index", "Jobs")">
                                    <i class="fas fa-tasks"></i> Jobs
                                </a>
                            </li>
                        }

                        @if (User.HasPermission("Reports.View"))
                        {
                            <li class="nav-item">
                                <a class="nav-link" href="@Url.Action("Index", "Reports")">
                                    <i class="fas fa-chart-bar"></i> Reports
                                </a>
                            </li>
                        }
                    </ul>

                    <ul class="navbar-nav">
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> @User.FindFirst("FullName")?.Value
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="@Url.Action("Profile", "Account")">Profile</a></li>
                                <li><a class="dropdown-item" href="@Url.Action("ChangePassword", "Account")">Change Password</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="@Url.Action("Logout", "Account")">Logout</a></li>
                            </ul>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    }

    <main class="@(User.Identity?.IsAuthenticated == true ? "container-fluid mt-4" : "")">
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                @TempData["SuccessMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                @TempData["ErrorMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        }

        @RenderBody()
    </main>

    @if (User.Identity?.IsAuthenticated == true)
    {
        <footer class="bg-light text-center text-muted py-3 mt-5">
            <div class="container">
                &copy; 2025 CRM System. All rights reserved.
            </div>
        </footer>
    }

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
