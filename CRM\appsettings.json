{"ConnectionStrings": {"DefaultConnection": "Server=DESKTOP-3IQHMH9;Database=eSORSDB;Trusted_Connection=True;TrustServerCertificate=True;Encrypt=False;Integrated Security=True;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "JwtSettings": {"SecretKey": "YourSuperSecretKeyForJWTTokenGeneration123456789", "Issuer": "CRMSystem", "Audience": "CRMUsers", "ExpirationInMinutes": 60}, "NotificationSettings": {"EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "EnableSsl": true, "Username": "", "Password": ""}, "RenewalAlertDays": [90, 60, 30], "LowStockThreshold": 10}}