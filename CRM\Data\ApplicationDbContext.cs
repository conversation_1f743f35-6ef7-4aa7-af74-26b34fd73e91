using CRM.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace CRM.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<Customer> Customers { get; set; }
        public DbSet<Device> Devices { get; set; }
        public DbSet<SIMCard> SIMCards { get; set; }
        public DbSet<Sale> Sales { get; set; }
        public DbSet<SaleItem> SaleItems { get; set; }
        public DbSet<InventoryItem> InventoryItems { get; set; }
        public DbSet<InventoryAssignment> InventoryAssignments { get; set; }
        public DbSet<StockTransfer> StockTransfers { get; set; }
        public DbSet<Query> Queries { get; set; }
        public DbSet<QueryComment> QueryComments { get; set; }
        public DbSet<QueryAttachment> QueryAttachments { get; set; }
        public DbSet<Job> Jobs { get; set; }
        public DbSet<JobPhoto> JobPhotos { get; set; }
        public DbSet<JobUpdate> JobUpdates { get; set; }
        public DbSet<Notification> Notifications { get; set; }
        public DbSet<RenewalAlert> RenewalAlerts { get; set; }
        public DbSet<InventoryAlert> InventoryAlerts { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure Identity tables
            builder.Entity<ApplicationUser>(entity =>
            {
                entity.ToTable("Users");
                entity.Property(e => e.Role).HasConversion<int>();
            });

            builder.Entity<IdentityRole>().ToTable("Roles");
            builder.Entity<IdentityUserRole<string>>().ToTable("UserRoles");
            builder.Entity<IdentityUserClaim<string>>().ToTable("UserClaims");
            builder.Entity<IdentityUserLogin<string>>().ToTable("UserLogins");
            builder.Entity<IdentityUserToken<string>>().ToTable("UserTokens");
            builder.Entity<IdentityRoleClaim<string>>().ToTable("RoleClaims");

            // Configure enum conversions
            builder.Entity<Query>()
                .Property(e => e.Status)
                .HasConversion<int>();

            builder.Entity<Query>()
                .Property(e => e.Priority)
                .HasConversion<int>();

            builder.Entity<Job>()
                .Property(e => e.Status)
                .HasConversion<int>();

            builder.Entity<Job>()
                .Property(e => e.Priority)
                .HasConversion<int>();

            builder.Entity<JobUpdate>()
                .Property(e => e.PreviousStatus)
                .HasConversion<int>();

            builder.Entity<JobUpdate>()
                .Property(e => e.NewStatus)
                .HasConversion<int>();

            builder.Entity<InventoryItem>()
                .Property(e => e.Status)
                .HasConversion<int>();

            builder.Entity<InventoryItem>()
                .Property(e => e.DeadStockCategory)
                .HasConversion<int>();

            builder.Entity<StockTransfer>()
                .Property(e => e.Reason)
                .HasConversion<int>();

            builder.Entity<Device>()
                .Property(e => e.DeviceType)
                .HasConversion<int>();

            builder.Entity<Notification>()
                .Property(e => e.Type)
                .HasConversion<int>();

            // Configure relationships
            ConfigureUserRelationships(builder);
            ConfigureCustomerRelationships(builder);
            ConfigureSaleRelationships(builder);
            ConfigureInventoryRelationships(builder);
            ConfigureQueryRelationships(builder);
            ConfigureJobRelationships(builder);
            ConfigureNotificationRelationships(builder);

            // Configure indexes
            ConfigureIndexes(builder);

            // Seed data
            SeedData(builder);
        }

        private static void ConfigureUserRelationships(ModelBuilder builder)
        {
            // User -> Sales (One-to-Many)
            builder.Entity<Sale>()
                .HasOne(s => s.Salesperson)
                .WithMany(u => u.Sales)
                .HasForeignKey(s => s.SalespersonId)
                .OnDelete(DeleteBehavior.Restrict);

            // User -> Queries Created (One-to-Many)
            builder.Entity<Query>()
                .HasOne(q => q.CreatedByUser)
                .WithMany(u => u.QueriesCreated)
                .HasForeignKey(q => q.CreatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // User -> Queries Processed (One-to-Many)
            builder.Entity<Query>()
                .HasOne(q => q.ProcessedByUser)
                .WithMany(u => u.QueriesProcessed)
                .HasForeignKey(q => q.ProcessedByUserId)
                .OnDelete(DeleteBehavior.SetNull);

            // User -> Jobs (One-to-Many)
            builder.Entity<Job>()
                .HasOne(j => j.AssignedToUser)
                .WithMany(u => u.Jobs)
                .HasForeignKey(j => j.AssignedToUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // User -> Notifications (One-to-Many)
            builder.Entity<Notification>()
                .HasOne(n => n.User)
                .WithMany(u => u.Notifications)
                .HasForeignKey(n => n.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            // User -> AuditLogs (One-to-Many)
            builder.Entity<AuditLog>()
                .HasOne(a => a.User)
                .WithMany(u => u.AuditLogs)
                .HasForeignKey(a => a.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // User -> InventoryAssignments (AssignedTo) (One-to-Many)
            builder.Entity<InventoryAssignment>()
                .HasOne(ia => ia.AssignedToUser)
                .WithMany(u => u.InventoryAssignments)
                .HasForeignKey(ia => ia.AssignedToUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // User -> InventoryAssignments (AssignedBy) (One-to-Many)
            builder.Entity<InventoryAssignment>()
                .HasOne(ia => ia.AssignedByUser)
                .WithMany()
                .HasForeignKey(ia => ia.AssignedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private static void ConfigureCustomerRelationships(ModelBuilder builder)
        {
            // Customer -> Sales (One-to-Many)
            builder.Entity<Sale>()
                .HasOne(s => s.Customer)
                .WithMany(c => c.Sales)
                .HasForeignKey(s => s.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private static void ConfigureSaleRelationships(ModelBuilder builder)
        {
            // Sale -> SaleItems (One-to-Many)
            builder.Entity<SaleItem>()
                .HasOne(si => si.Sale)
                .WithMany(s => s.SaleItems)
                .HasForeignKey(si => si.SaleId)
                .OnDelete(DeleteBehavior.Cascade);

            // Sale -> Queries (One-to-Many)
            builder.Entity<Query>()
                .HasOne(q => q.Sale)
                .WithMany(s => s.Queries)
                .HasForeignKey(q => q.SaleId)
                .OnDelete(DeleteBehavior.Restrict);

            // Sale -> Jobs (One-to-Many)
            builder.Entity<Job>()
                .HasOne(j => j.Sale)
                .WithMany(s => s.Jobs)
                .HasForeignKey(j => j.SaleId)
                .OnDelete(DeleteBehavior.Restrict);

            // Sale -> RenewalAlerts (One-to-Many)
            builder.Entity<RenewalAlert>()
                .HasOne(ra => ra.Sale)
                .WithMany(s => s.RenewalAlerts)
                .HasForeignKey(ra => ra.SaleId)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private static void ConfigureInventoryRelationships(ModelBuilder builder)
        {
            // Device -> InventoryItems (One-to-Many)
            builder.Entity<InventoryItem>()
                .HasOne(ii => ii.Device)
                .WithMany(d => d.InventoryItems)
                .HasForeignKey(ii => ii.DeviceId)
                .OnDelete(DeleteBehavior.Restrict);

            // SIMCard -> InventoryItems (One-to-Many)
            builder.Entity<InventoryItem>()
                .HasOne(ii => ii.SIMCard)
                .WithMany(s => s.InventoryItems)
                .HasForeignKey(ii => ii.SIMCardId)
                .OnDelete(DeleteBehavior.Restrict);

            // Device -> SaleItems (One-to-Many)
            builder.Entity<SaleItem>()
                .HasOne(si => si.Device)
                .WithMany(d => d.SaleItems)
                .HasForeignKey(si => si.DeviceId)
                .OnDelete(DeleteBehavior.Restrict);

            // SIMCard -> SaleItems (One-to-Many)
            builder.Entity<SaleItem>()
                .HasOne(si => si.SIMCard)
                .WithMany(s => s.SaleItems)
                .HasForeignKey(si => si.SIMCardId)
                .OnDelete(DeleteBehavior.Restrict);

            // InventoryItem -> InventoryAssignments (One-to-Many)
            builder.Entity<InventoryAssignment>()
                .HasOne(ia => ia.InventoryItem)
                .WithMany(ii => ii.Assignments)
                .HasForeignKey(ia => ia.InventoryItemId)
                .OnDelete(DeleteBehavior.Cascade);

            // InventoryItem -> StockTransfers (One-to-Many)
            builder.Entity<StockTransfer>()
                .HasOne(st => st.InventoryItem)
                .WithMany(ii => ii.TransfersFrom)
                .HasForeignKey(st => st.InventoryItemId)
                .OnDelete(DeleteBehavior.Cascade);

            // Configure StockTransfer relationships
            builder.Entity<StockTransfer>()
                .HasOne(st => st.FromUser)
                .WithMany()
                .HasForeignKey(st => st.FromUserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StockTransfer>()
                .HasOne(st => st.ToUser)
                .WithMany()
                .HasForeignKey(st => st.ToUserId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StockTransfer>()
                .HasOne(st => st.FromCustomer)
                .WithMany()
                .HasForeignKey(st => st.FromCustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StockTransfer>()
                .HasOne(st => st.ToCustomer)
                .WithMany()
                .HasForeignKey(st => st.ToCustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<StockTransfer>()
                .HasOne(st => st.InitiatedByUser)
                .WithMany()
                .HasForeignKey(st => st.InitiatedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // Configure InventoryItem relationships with Users and Customers
            builder.Entity<InventoryItem>()
                .HasOne(ii => ii.AssignedToUser)
                .WithMany()
                .HasForeignKey(ii => ii.AssignedToUserId)
                .OnDelete(DeleteBehavior.SetNull);

            builder.Entity<InventoryItem>()
                .HasOne(ii => ii.AssignedToCustomer)
                .WithMany()
                .HasForeignKey(ii => ii.AssignedToCustomerId)
                .OnDelete(DeleteBehavior.SetNull);
        }

        private static void ConfigureQueryRelationships(ModelBuilder builder)
        {
            // Query -> QueryComments (One-to-Many)
            builder.Entity<QueryComment>()
                .HasOne(qc => qc.Query)
                .WithMany(q => q.Comments)
                .HasForeignKey(qc => qc.QueryId)
                .OnDelete(DeleteBehavior.Cascade);

            // Query -> QueryAttachments (One-to-Many)
            builder.Entity<QueryAttachment>()
                .HasOne(qa => qa.Query)
                .WithMany(q => q.Attachments)
                .HasForeignKey(qa => qa.QueryId)
                .OnDelete(DeleteBehavior.Cascade);

            // QueryComment -> User
            builder.Entity<QueryComment>()
                .HasOne(qc => qc.User)
                .WithMany()
                .HasForeignKey(qc => qc.UserId)
                .OnDelete(DeleteBehavior.Restrict);

            // QueryAttachment -> User
            builder.Entity<QueryAttachment>()
                .HasOne(qa => qa.UploadedByUser)
                .WithMany()
                .HasForeignKey(qa => qa.UploadedByUserId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private static void ConfigureJobRelationships(ModelBuilder builder)
        {
            // Job -> JobPhotos (One-to-Many)
            builder.Entity<JobPhoto>()
                .HasOne(jp => jp.Job)
                .WithMany(j => j.Photos)
                .HasForeignKey(jp => jp.JobId)
                .OnDelete(DeleteBehavior.Cascade);

            // Job -> JobUpdates (One-to-Many)
            builder.Entity<JobUpdate>()
                .HasOne(ju => ju.Job)
                .WithMany(j => j.Updates)
                .HasForeignKey(ju => ju.JobId)
                .OnDelete(DeleteBehavior.Cascade);

            // JobPhoto -> User
            builder.Entity<JobPhoto>()
                .HasOne(jp => jp.UploadedByUser)
                .WithMany()
                .HasForeignKey(jp => jp.UploadedByUserId)
                .OnDelete(DeleteBehavior.Restrict);

            // JobUpdate -> User
            builder.Entity<JobUpdate>()
                .HasOne(ju => ju.User)
                .WithMany()
                .HasForeignKey(ju => ju.UserId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private static void ConfigureNotificationRelationships(ModelBuilder builder)
        {
            // Device -> InventoryAlerts (One-to-Many)
            builder.Entity<InventoryAlert>()
                .HasOne(ia => ia.Device)
                .WithMany()
                .HasForeignKey(ia => ia.DeviceId)
                .OnDelete(DeleteBehavior.Cascade);

            // SIMCard -> InventoryAlerts (One-to-Many)
            builder.Entity<InventoryAlert>()
                .HasOne(ia => ia.SIMCard)
                .WithMany()
                .HasForeignKey(ia => ia.SIMCardId)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private static void ConfigureIndexes(ModelBuilder builder)
        {
            // User indexes
            builder.Entity<ApplicationUser>()
                .HasIndex(u => u.EmployeeId)
                .IsUnique()
                .HasFilter("[EmployeeId] IS NOT NULL");

            builder.Entity<ApplicationUser>()
                .HasIndex(u => u.Role);

            // Customer indexes
            builder.Entity<Customer>()
                .HasIndex(c => c.Email);

            // Sale indexes
            builder.Entity<Sale>()
                .HasIndex(s => s.SaleNumber)
                .IsUnique();

            builder.Entity<Sale>()
                .HasIndex(s => s.RenewalDate);

            // Inventory indexes
            builder.Entity<InventoryItem>()
                .HasIndex(i => i.SerialNumber)
                .IsUnique();

            builder.Entity<InventoryItem>()
                .HasIndex(i => i.Status);

            // Query indexes
            builder.Entity<Query>()
                .HasIndex(q => q.QueryNumber)
                .IsUnique();

            builder.Entity<Query>()
                .HasIndex(q => q.Status);

            builder.Entity<Query>()
                .HasIndex(q => q.Priority);

            // Job indexes
            builder.Entity<Job>()
                .HasIndex(j => j.JobNumber)
                .IsUnique();

            builder.Entity<Job>()
                .HasIndex(j => j.Status);

            builder.Entity<Job>()
                .HasIndex(j => j.ScheduledDate);

            // Notification indexes
            builder.Entity<Notification>()
                .HasIndex(n => new { n.UserId, n.IsRead });

            builder.Entity<Notification>()
                .HasIndex(n => n.CreatedAt);
        }

        private static void SeedData(ModelBuilder builder)
        {
            // Seed Roles
            var roles = new[]
            {
                new IdentityRole { Id = "1", Name = "Salesperson", NormalizedName = "SALESPERSON" },
                new IdentityRole { Id = "2", Name = "OperationsStaff", NormalizedName = "OPERATIONSSTAFF" },
                new IdentityRole { Id = "3", Name = "Technician", NormalizedName = "TECHNICIAN" },
                new IdentityRole { Id = "4", Name = "Management", NormalizedName = "MANAGEMENT" }
            };

            builder.Entity<IdentityRole>().HasData(roles);

            // Seed Device Types
            var devices = new[]
            {
                new Device
                {
                    DeviceId = 1,
                    DeviceName = "GPS Tracker Pro",
                    DeviceType = DeviceType.GPSTracker,
                    Model = "GT-2000",
                    Manufacturer = "TrackTech",
                    Description = "Professional GPS tracking device with real-time monitoring",
                    UnitPrice = 299.99m,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new Device
                {
                    DeviceId = 2,
                    DeviceName = "Security Camera HD",
                    DeviceType = DeviceType.SecurityCamera,
                    Model = "SC-1080",
                    Manufacturer = "SecureTech",
                    Description = "High-definition security camera with night vision",
                    UnitPrice = 199.99m,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            builder.Entity<Device>().HasData(devices);

            // Seed SIM Cards
            var simCards = new[]
            {
                new SIMCard
                {
                    SIMCardId = 1,
                    SIMNumber = "1234567890",
                    ICCID = "89012345678901234567",
                    Carrier = "Verizon",
                    PlanType = "IoT Data Plan",
                    MonthlyFee = 15.00m,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                },
                new SIMCard
                {
                    SIMCardId = 2,
                    SIMNumber = "0987654321",
                    ICCID = "89012345678901234568",
                    Carrier = "AT&T",
                    PlanType = "IoT Data Plan",
                    MonthlyFee = 12.00m,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                }
            };

            builder.Entity<SIMCard>().HasData(simCards);
        }
    }
}
