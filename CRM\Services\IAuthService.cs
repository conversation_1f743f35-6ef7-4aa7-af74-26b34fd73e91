using CRM.Models;
using CRM.Models.ViewModels;
using Microsoft.AspNetCore.Identity;

namespace CRM.Services
{
    public interface IAuthService
    {
        Task<IdentityResult> RegisterUserAsync(RegisterViewModel model);
        Task<SignInResult> LoginAsync(LoginViewModel model);
        Task LogoutAsync();
        Task<ApplicationUser?> GetUserByIdAsync(string userId);
        Task<ApplicationUser?> GetUserByEmailAsync(string email);
        Task<IdentityResult> UpdateUserAsync(UserProfileViewModel model);
        Task<IdentityResult> ChangePasswordAsync(string userId, ChangePasswordViewModel model);
        Task<bool> IsInRoleAsync(ApplicationUser user, UserRole role);
        Task<bool> HasPermissionAsync(string userId, string permission);
        Task<List<ApplicationUser>> GetUsersByRoleAsync(UserRole role);
        Task<IdentityResult> DeactivateUserAsync(string userId);
        Task<IdentityResult> ActivateUserAsync(string userId);
        Task<string> GeneratePasswordResetTokenAsync(ApplicationUser user);
        Task<IdentityResult> ResetPasswordAsync(ResetPasswordViewModel model);
        Task UpdateLastLoginAsync(string userId);
    }

    public class AuthService : IAuthService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            ILogger<AuthService> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _logger = logger;
        }

        public async Task<IdentityResult> RegisterUserAsync(RegisterViewModel model)
        {
            var user = new ApplicationUser
            {
                UserName = model.Email,
                Email = model.Email,
                FirstName = model.FirstName,
                LastName = model.LastName,
                Role = model.Role,
                EmployeeId = model.EmployeeId,
                Department = model.Department,
                PhoneNumber = model.PhoneNumber,
                Notes = model.Notes,
                IsActive = true,
                CreatedAt = DateTime.UtcNow
            };

            var result = await _userManager.CreateAsync(user, model.Password);
            
            if (result.Succeeded)
            {
                await _userManager.AddToRoleAsync(user, model.Role.ToString());
                _logger.LogInformation("User {Email} registered successfully with role {Role}", model.Email, model.Role);
            }

            return result;
        }

        public async Task<SignInResult> LoginAsync(LoginViewModel model)
        {
            var user = await _userManager.FindByEmailAsync(model.Email);
            if (user == null || !user.IsActive)
            {
                return SignInResult.Failed;
            }

            var result = await _signInManager.PasswordSignInAsync(
                user.UserName!, 
                model.Password, 
                model.RememberMe, 
                lockoutOnFailure: true);

            if (result.Succeeded)
            {
                await UpdateLastLoginAsync(user.Id);
                _logger.LogInformation("User {Email} logged in successfully", model.Email);
            }

            return result;
        }

        public async Task LogoutAsync()
        {
            await _signInManager.SignOutAsync();
        }

        public async Task<ApplicationUser?> GetUserByIdAsync(string userId)
        {
            return await _userManager.FindByIdAsync(userId);
        }

        public async Task<ApplicationUser?> GetUserByEmailAsync(string email)
        {
            return await _userManager.FindByEmailAsync(email);
        }

        public async Task<IdentityResult> UpdateUserAsync(UserProfileViewModel model)
        {
            var user = await _userManager.FindByIdAsync(model.Id);
            if (user == null)
            {
                return IdentityResult.Failed(new IdentityError { Description = "User not found" });
            }

            user.FirstName = model.FirstName;
            user.LastName = model.LastName;
            user.EmployeeId = model.EmployeeId;
            user.Department = model.Department;
            user.PhoneNumber = model.PhoneNumber;
            user.Notes = model.Notes;
            user.IsActive = model.IsActive;

            return await _userManager.UpdateAsync(user);
        }

        public async Task<IdentityResult> ChangePasswordAsync(string userId, ChangePasswordViewModel model)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return IdentityResult.Failed(new IdentityError { Description = "User not found" });
            }

            return await _userManager.ChangePasswordAsync(user, model.OldPassword, model.NewPassword);
        }

        public async Task<bool> IsInRoleAsync(ApplicationUser user, UserRole role)
        {
            return await _userManager.IsInRoleAsync(user, role.ToString());
        }

        public async Task<bool> HasPermissionAsync(string userId, string permission)
        {
            var user = await GetUserByIdAsync(userId);
            if (user == null || !user.IsActive)
                return false;

            // Define role-based permissions
            var rolePermissions = new Dictionary<UserRole, List<string>>
            {
                [UserRole.Salesperson] = new List<string>
                {
                    "Sales.Create", "Sales.View", "Sales.Edit",
                    "Customers.Create", "Customers.View", "Customers.Edit",
                    "Queries.Create", "Queries.View"
                },
                [UserRole.OperationsStaff] = new List<string>
                {
                    "Sales.View", "Customers.View",
                    "Queries.View", "Queries.Process", "Queries.Approve",
                    "Inventory.View", "Inventory.Assign", "Inventory.Transfer",
                    "Jobs.Create", "Jobs.Assign"
                },
                [UserRole.Technician] = new List<string>
                {
                    "Jobs.View", "Jobs.Update", "Jobs.Complete",
                    "Inventory.View"
                },
                [UserRole.Management] = new List<string>
                {
                    "Sales.View", "Customers.View", "Queries.View",
                    "Inventory.View", "Jobs.View", "Reports.View",
                    "Users.View", "Users.Manage"
                }
            };

            return rolePermissions.ContainsKey(user.Role) && 
                   rolePermissions[user.Role].Contains(permission);
        }

        public async Task<List<ApplicationUser>> GetUsersByRoleAsync(UserRole role)
        {
            var usersInRole = await _userManager.GetUsersInRoleAsync(role.ToString());
            return usersInRole.Where(u => u.IsActive).ToList();
        }

        public async Task<IdentityResult> DeactivateUserAsync(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return IdentityResult.Failed(new IdentityError { Description = "User not found" });
            }

            user.IsActive = false;
            return await _userManager.UpdateAsync(user);
        }

        public async Task<IdentityResult> ActivateUserAsync(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return IdentityResult.Failed(new IdentityError { Description = "User not found" });
            }

            user.IsActive = true;
            return await _userManager.UpdateAsync(user);
        }

        public async Task<string> GeneratePasswordResetTokenAsync(ApplicationUser user)
        {
            return await _userManager.GeneratePasswordResetTokenAsync(user);
        }

        public async Task<IdentityResult> ResetPasswordAsync(ResetPasswordViewModel model)
        {
            var user = await _userManager.FindByEmailAsync(model.Email);
            if (user == null)
            {
                return IdentityResult.Failed(new IdentityError { Description = "User not found" });
            }

            return await _userManager.ResetPasswordAsync(user, model.Code, model.Password);
        }

        public async Task UpdateLastLoginAsync(string userId)
        {
            var user = await _userManager.FindByIdAsync(userId);
            if (user != null)
            {
                user.LastLoginAt = DateTime.UtcNow;
                await _userManager.UpdateAsync(user);
            }
        }
    }
}
