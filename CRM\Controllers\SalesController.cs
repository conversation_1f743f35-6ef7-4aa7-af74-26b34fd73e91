using CRM.Attributes;
using CRM.Data;
using CRM.Models;
using CRM.Models.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace CRM.Controllers
{
    [Authorize]
    [AuthorizePermission("Sales.View")]
    public class SalesController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<SalesController> _logger;

        public SalesController(ApplicationDbContext context, ILogger<SalesController> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<IActionResult> Index(int page = 1, int pageSize = 20)
        {
            var userId = User.GetUserId()!;
            var userRole = User.GetUserRole();

            var query = _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.Salesperson)
                .Include(s => s.SaleItems)
                .Where(s => s.IsActive);

            // Filter by salesperson for non-management roles
            if (userRole == UserRole.Salesperson)
            {
                query = query.Where(s => s.SalespersonId == userId);
            }

            var totalCount = await query.CountAsync();
            var sales = await query
                .OrderByDescending(s => s.SaleDate)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .Select(s => new SaleSummaryViewModel
                {
                    SaleId = s.SaleId,
                    SaleNumber = s.SaleNumber,
                    CustomerName = s.Customer.FullName,
                    FinalAmount = s.FinalAmount,
                    SaleDate = s.SaleDate,
                    TotalDevices = s.SaleItems.Sum(si => si.Quantity),
                    RenewalDate = s.RenewalDate,
                    IsRenewalDue = s.RenewalDate <= DateTime.UtcNow.AddDays(90)
                })
                .ToListAsync();

            ViewBag.CurrentPage = page;
            ViewBag.PageSize = pageSize;
            ViewBag.TotalCount = totalCount;
            ViewBag.TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            return View(sales);
        }

        [AuthorizePermission("Sales.View")]
        public async Task<IActionResult> Details(int id)
        {
            var sale = await _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.Salesperson)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Device)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.SIMCard)
                .Include(s => s.Queries)
                .Include(s => s.Jobs)
                .FirstOrDefaultAsync(s => s.SaleId == id);

            if (sale == null)
            {
                return NotFound();
            }

            // Check if user can view this sale
            var userRole = User.GetUserRole();
            if (userRole == UserRole.Salesperson && sale.SalespersonId != User.GetUserId())
            {
                return Forbid();
            }

            return View(sale);
        }

        [HttpGet]
        [AuthorizePermission("Sales.Create")]
        public async Task<IActionResult> Create()
        {
            var model = new CreateSaleViewModel();
            await PopulateCreateSaleViewModel(model);
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorizePermission("Sales.Create")]
        public async Task<IActionResult> Create(CreateSaleViewModel model)
        {
            if (!ModelState.IsValid)
            {
                await PopulateCreateSaleViewModel(model);
                return View(model);
            }

            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // Create or find customer
                var customer = await CreateOrUpdateCustomer(model);
                await _context.SaveChangesAsync();

                // Generate sale number
                var saleNumber = await GenerateSaleNumber();

                // Create sale
                var sale = new Sale
                {
                    SaleNumber = saleNumber,
                    CustomerId = customer.CustomerId,
                    SalespersonId = User.GetUserId()!,
                    TotalAmount = model.TotalAmount,
                    DiscountAmount = model.DiscountAmount,
                    FinalAmount = model.FinalAmount,
                    PaymentTerms = model.PaymentTerms,
                    AnnualRenewalAmount = model.AnnualRenewalAmount,
                    RenewalDate = model.RenewalDate,
                    AutoRenewal = model.AutoRenewal,
                    SaleDate = DateTime.UtcNow,
                    Notes = model.Notes,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Sales.Add(sale);
                await _context.SaveChangesAsync();

                // Create sale items
                foreach (var item in model.SaleItems)
                {
                    var saleItem = new SaleItem
                    {
                        SaleId = sale.SaleId,
                        DeviceId = item.DeviceId,
                        SIMCardId = item.SIMCardId,
                        Quantity = item.Quantity,
                        UnitPrice = item.UnitPrice,
                        TotalPrice = item.TotalPrice,
                        SerialNumbers = item.SerialNumbers,
                        Notes = item.Notes
                    };

                    _context.SaleItems.Add(saleItem);
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                _logger.LogInformation("Sale {SaleNumber} created successfully by user {UserId}", 
                    saleNumber, User.GetUserId());

                TempData["SuccessMessage"] = $"Sale {saleNumber} created successfully!";
                return RedirectToAction("Details", new { id = sale.SaleId });
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error creating sale");
                ModelState.AddModelError(string.Empty, "An error occurred while creating the sale.");
                await PopulateCreateSaleViewModel(model);
                return View(model);
            }
        }

        [HttpGet]
        [AuthorizePermission("Sales.Edit")]
        public async Task<IActionResult> Edit(int id)
        {
            var sale = await _context.Sales
                .Include(s => s.Customer)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.Device)
                .Include(s => s.SaleItems)
                    .ThenInclude(si => si.SIMCard)
                .FirstOrDefaultAsync(s => s.SaleId == id);

            if (sale == null)
            {
                return NotFound();
            }

            // Check if user can edit this sale
            var userRole = User.GetUserRole();
            if (userRole == UserRole.Salesperson && sale.SalespersonId != User.GetUserId())
            {
                return Forbid();
            }

            var model = new CreateSaleViewModel
            {
                CustomerFirstName = sale.Customer.FirstName,
                CustomerLastName = sale.Customer.LastName,
                CustomerCompanyName = sale.Customer.CompanyName,
                CustomerEmail = sale.Customer.Email,
                CustomerPrimaryPhone = sale.Customer.PrimaryPhone,
                CustomerSecondaryPhone = sale.Customer.SecondaryPhone,
                BillingAddress = sale.Customer.BillingAddress,
                BillingCity = sale.Customer.BillingCity,
                BillingState = sale.Customer.BillingState,
                BillingZipCode = sale.Customer.BillingZipCode,
                BillingCountry = sale.Customer.BillingCountry,
                InstallationAddress = sale.Customer.InstallationAddress,
                InstallationCity = sale.Customer.InstallationCity,
                InstallationState = sale.Customer.InstallationState,
                InstallationZipCode = sale.Customer.InstallationZipCode,
                InstallationCountry = sale.Customer.InstallationCountry,
                DiscountAmount = sale.DiscountAmount,
                PaymentTerms = sale.PaymentTerms,
                AnnualRenewalAmount = sale.AnnualRenewalAmount,
                RenewalDate = sale.RenewalDate,
                AutoRenewal = sale.AutoRenewal,
                Notes = sale.Notes,
                SaleItems = sale.SaleItems.Select(si => new SaleItemViewModel
                {
                    DeviceId = si.DeviceId,
                    SIMCardId = si.SIMCardId,
                    Quantity = si.Quantity,
                    UnitPrice = si.UnitPrice,
                    SerialNumbers = si.SerialNumbers,
                    Notes = si.Notes,
                    DeviceName = si.Device?.DisplayName,
                    SIMCardName = si.SIMCard?.DisplayName
                }).ToList()
            };

            await PopulateCreateSaleViewModel(model);
            ViewBag.SaleId = id;
            return View(model);
        }

        private async Task<Customer> CreateOrUpdateCustomer(CreateSaleViewModel model)
        {
            // Check if customer already exists by email
            var existingCustomer = await _context.Customers
                .FirstOrDefaultAsync(c => c.Email == model.CustomerEmail);

            if (existingCustomer != null)
            {
                // Update existing customer
                existingCustomer.FirstName = model.CustomerFirstName;
                existingCustomer.LastName = model.CustomerLastName;
                existingCustomer.CompanyName = model.CustomerCompanyName;
                existingCustomer.PrimaryPhone = model.CustomerPrimaryPhone;
                existingCustomer.SecondaryPhone = model.CustomerSecondaryPhone;
                existingCustomer.BillingAddress = model.BillingAddress;
                existingCustomer.BillingCity = model.BillingCity;
                existingCustomer.BillingState = model.BillingState;
                existingCustomer.BillingZipCode = model.BillingZipCode;
                existingCustomer.BillingCountry = model.BillingCountry;
                existingCustomer.InstallationAddress = model.InstallationAddress;
                existingCustomer.InstallationCity = model.InstallationCity;
                existingCustomer.InstallationState = model.InstallationState;
                existingCustomer.InstallationZipCode = model.InstallationZipCode;
                existingCustomer.InstallationCountry = model.InstallationCountry;
                existingCustomer.UpdatedAt = DateTime.UtcNow;

                return existingCustomer;
            }
            else
            {
                // Create new customer
                var customer = new Customer
                {
                    FirstName = model.CustomerFirstName,
                    LastName = model.CustomerLastName,
                    CompanyName = model.CustomerCompanyName,
                    Email = model.CustomerEmail,
                    PrimaryPhone = model.CustomerPrimaryPhone,
                    SecondaryPhone = model.CustomerSecondaryPhone,
                    BillingAddress = model.BillingAddress,
                    BillingCity = model.BillingCity,
                    BillingState = model.BillingState,
                    BillingZipCode = model.BillingZipCode,
                    BillingCountry = model.BillingCountry,
                    InstallationAddress = model.InstallationAddress,
                    InstallationCity = model.InstallationCity,
                    InstallationState = model.InstallationState,
                    InstallationZipCode = model.InstallationZipCode,
                    InstallationCountry = model.InstallationCountry,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                _context.Customers.Add(customer);
                return customer;
            }
        }

        private async Task<string> GenerateSaleNumber()
        {
            var today = DateTime.UtcNow;
            var prefix = $"S{today:yyyyMM}";
            
            var lastSale = await _context.Sales
                .Where(s => s.SaleNumber.StartsWith(prefix))
                .OrderByDescending(s => s.SaleNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (lastSale != null)
            {
                var lastNumberPart = lastSale.SaleNumber.Substring(prefix.Length);
                if (int.TryParse(lastNumberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{prefix}{nextNumber:D4}";
        }

        private async Task PopulateCreateSaleViewModel(CreateSaleViewModel model)
        {
            // This method would populate dropdown lists for devices, SIM cards, etc.
            // For now, we'll leave it empty as we'll implement the full functionality later
            await Task.CompletedTask;
        }
    }
}
