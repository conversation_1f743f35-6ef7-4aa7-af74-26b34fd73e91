using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CRM.Models
{
    public class InventoryItem
    {
        [Key]
        public int InventoryItemId { get; set; }

        public int? DeviceId { get; set; }

        public int? SIMCardId { get; set; }

        [Required]
        [StringLength(100)]
        public string SerialNumber { get; set; } = string.Empty;

        [Required]
        public InventoryStatus Status { get; set; } = InventoryStatus.Available;

        public DeadStockCategory? DeadStockCategory { get; set; }

        [StringLength(500)]
        public string? DeadStockReason { get; set; }

        [StringLength(200)]
        public string? CurrentLocation { get; set; }

        public string? AssignedToUserId { get; set; }

        public int? AssignedToCustomerId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Foreign Keys
        [ForeignKey("DeviceId")]
        public virtual Device? Device { get; set; }

        [ForeignKey("SIMCardId")]
        public virtual SIMCard? SIMCard { get; set; }

        [ForeignKey("AssignedToUserId")]
        public virtual ApplicationUser? AssignedToUser { get; set; }

        [ForeignKey("AssignedToCustomerId")]
        public virtual Customer? AssignedToCustomer { get; set; }

        // Navigation properties
        public virtual ICollection<InventoryAssignment> Assignments { get; set; } = new List<InventoryAssignment>();
        public virtual ICollection<StockTransfer> TransfersFrom { get; set; } = new List<StockTransfer>();
        public virtual ICollection<StockTransfer> TransfersTo { get; set; } = new List<StockTransfer>();

        [NotMapped]
        public string ItemDescription => Device?.DisplayName ?? SIMCard?.DisplayName ?? "Unknown Item";

        [NotMapped]
        public string StatusDisplay => Status.ToString();

        [NotMapped]
        public bool IsAvailable => Status == InventoryStatus.Available;
    }

    public class InventoryAssignment
    {
        [Key]
        public int AssignmentId { get; set; }

        [Required]
        public int InventoryItemId { get; set; }

        [Required]
        public string AssignedToUserId { get; set; } = string.Empty;

        [Required]
        public string AssignedByUserId { get; set; } = string.Empty;

        public DateTime AssignedAt { get; set; } = DateTime.UtcNow;

        public DateTime? ReturnedAt { get; set; }

        [StringLength(500)]
        public string? AssignmentReason { get; set; }

        [StringLength(500)]
        public string? ReturnReason { get; set; }

        [StringLength(1000)]
        public string? Notes { get; set; }

        public bool IsActive { get; set; } = true;

        // Foreign Keys
        [ForeignKey("InventoryItemId")]
        public virtual InventoryItem InventoryItem { get; set; } = null!;

        [ForeignKey("AssignedToUserId")]
        public virtual ApplicationUser AssignedToUser { get; set; } = null!;

        [ForeignKey("AssignedByUserId")]
        public virtual ApplicationUser AssignedByUser { get; set; } = null!;

        [NotMapped]
        public TimeSpan? AssignmentDuration => ReturnedAt?.Subtract(AssignedAt);
    }

    public class StockTransfer
    {
        [Key]
        public int TransferId { get; set; }

        [Required]
        public int InventoryItemId { get; set; }

        public string? FromUserId { get; set; }

        public string? ToUserId { get; set; }

        public int? FromCustomerId { get; set; }

        public int? ToCustomerId { get; set; }

        [StringLength(200)]
        public string? FromLocation { get; set; }

        [StringLength(200)]
        public string? ToLocation { get; set; }

        [Required]
        public TransferReason Reason { get; set; }

        [Required]
        public string InitiatedByUserId { get; set; } = string.Empty;

        public DateTime TransferDate { get; set; } = DateTime.UtcNow;

        [StringLength(1000)]
        public string? Notes { get; set; }

        // Foreign Keys
        [ForeignKey("InventoryItemId")]
        public virtual InventoryItem InventoryItem { get; set; } = null!;

        [ForeignKey("FromUserId")]
        public virtual ApplicationUser? FromUser { get; set; }

        [ForeignKey("ToUserId")]
        public virtual ApplicationUser? ToUser { get; set; }

        [ForeignKey("FromCustomerId")]
        public virtual Customer? FromCustomer { get; set; }

        [ForeignKey("ToCustomerId")]
        public virtual Customer? ToCustomer { get; set; }

        [ForeignKey("InitiatedByUserId")]
        public virtual ApplicationUser InitiatedByUser { get; set; } = null!;

        [NotMapped]
        public string TransferDescription => $"{FromLocation ?? FromUser?.FullName ?? FromCustomer?.FullName} → {ToLocation ?? ToUser?.FullName ?? ToCustomer?.FullName}";
    }
}
