using CRM.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using System.Security.Claims;

namespace CRM.Attributes
{
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
    public class AuthorizeRoleAttribute : Attribute, IAuthorizationFilter
    {
        private readonly UserRole[] _roles;

        public AuthorizeRoleAttribute(params UserRole[] roles)
        {
            _roles = roles;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            var user = context.HttpContext.User;

            if (!user.Identity?.IsAuthenticated ?? true)
            {
                context.Result = new RedirectToActionResult("Login", "Account", null);
                return;
            }

            var userRoleClaim = user.FindFirst("Role")?.Value;
            if (string.IsNullOrEmpty(userRoleClaim) || !Enum.TryParse<UserRole>(userRoleClaim, out var userRole))
            {
                context.Result = new ForbidResult();
                return;
            }

            if (!_roles.Contains(userRole))
            {
                context.Result = new ForbidResult();
                return;
            }
        }
    }

    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
    public class AuthorizePermissionAttribute : Attribute, IAuthorizationFilter
    {
        private readonly string _permission;

        public AuthorizePermissionAttribute(string permission)
        {
            _permission = permission;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            var user = context.HttpContext.User;

            if (!user.Identity?.IsAuthenticated ?? true)
            {
                context.Result = new RedirectToActionResult("Login", "Account", null);
                return;
            }

            var userRoleClaim = user.FindFirst("Role")?.Value;
            if (string.IsNullOrEmpty(userRoleClaim) || !Enum.TryParse<UserRole>(userRoleClaim, out var userRole))
            {
                context.Result = new ForbidResult();
                return;
            }

            if (!HasPermission(userRole, _permission))
            {
                context.Result = new ForbidResult();
                return;
            }
        }

        private static bool HasPermission(UserRole role, string permission)
        {
            var rolePermissions = new Dictionary<UserRole, List<string>>
            {
                [UserRole.Salesperson] = new List<string>
                {
                    "Sales.Create", "Sales.View", "Sales.Edit",
                    "Customers.Create", "Customers.View", "Customers.Edit",
                    "Queries.Create", "Queries.View"
                },
                [UserRole.OperationsStaff] = new List<string>
                {
                    "Sales.View", "Customers.View",
                    "Queries.View", "Queries.Process", "Queries.Approve",
                    "Inventory.View", "Inventory.Assign", "Inventory.Transfer",
                    "Jobs.Create", "Jobs.Assign"
                },
                [UserRole.Technician] = new List<string>
                {
                    "Jobs.View", "Jobs.Update", "Jobs.Complete",
                    "Inventory.View"
                },
                [UserRole.Management] = new List<string>
                {
                    "Sales.View", "Customers.View", "Queries.View",
                    "Inventory.View", "Jobs.View", "Reports.View",
                    "Users.View", "Users.Manage"
                }
            };

            return rolePermissions.ContainsKey(role) && rolePermissions[role].Contains(permission);
        }
    }

    public static class ClaimsPrincipalExtensions
    {
        public static string? GetUserId(this ClaimsPrincipal principal)
        {
            return principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        }

        public static string? GetUserEmail(this ClaimsPrincipal principal)
        {
            return principal.FindFirst(ClaimTypes.Email)?.Value;
        }

        public static string? GetUserName(this ClaimsPrincipal principal)
        {
            return principal.FindFirst(ClaimTypes.Name)?.Value;
        }

        public static UserRole? GetUserRole(this ClaimsPrincipal principal)
        {
            var roleClaim = principal.FindFirst("Role")?.Value;
            return Enum.TryParse<UserRole>(roleClaim, out var role) ? role : null;
        }

        public static bool HasPermission(this ClaimsPrincipal principal, string permission)
        {
            var role = principal.GetUserRole();
            if (!role.HasValue) return false;

            var rolePermissions = new Dictionary<UserRole, List<string>>
            {
                [UserRole.Salesperson] = new List<string>
                {
                    "Sales.Create", "Sales.View", "Sales.Edit",
                    "Customers.Create", "Customers.View", "Customers.Edit",
                    "Queries.Create", "Queries.View"
                },
                [UserRole.OperationsStaff] = new List<string>
                {
                    "Sales.View", "Customers.View",
                    "Queries.View", "Queries.Process", "Queries.Approve",
                    "Inventory.View", "Inventory.Assign", "Inventory.Transfer",
                    "Jobs.Create", "Jobs.Assign"
                },
                [UserRole.Technician] = new List<string>
                {
                    "Jobs.View", "Jobs.Update", "Jobs.Complete",
                    "Inventory.View"
                },
                [UserRole.Management] = new List<string>
                {
                    "Sales.View", "Customers.View", "Queries.View",
                    "Inventory.View", "Jobs.View", "Reports.View",
                    "Users.View", "Users.Manage"
                }
            };

            return rolePermissions.ContainsKey(role.Value) && rolePermissions[role.Value].Contains(permission);
        }
    }
}
