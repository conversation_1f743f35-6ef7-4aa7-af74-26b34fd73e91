2025-09-09 15:03:28.439 +05:00 [WRN] The foreign key property 'StockTransfer.InventoryItemId1' was created in shadow state because a conflicting property with the simple name 'InventoryItemId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-09-09 15:03:28.651 +05:00 [WRN] The foreign key property 'StockTransfer.InventoryItemId1' was created in shadow state because a conflicting property with the simple name 'InventoryItemId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-09-09 15:03:44.411 +05:00 [WRN] The foreign key property 'StockTransfer.InventoryItemId1' was created in shadow state because a conflicting property with the simple name 'InventoryItemId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-09-09 15:03:44.726 +05:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-09-09 15:03:44.747 +05:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-09-09 15:03:44.749 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-09-09 15:03:44.751 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-09-09 15:03:44.766 +05:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-09-09 15:03:44.779 +05:00 [INF] Applying migration '20250909100329_InitialCreate'.
2025-09-09 15:03:44.971 +05:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Customers] (
    [CustomerId] int NOT NULL IDENTITY,
    [FirstName] nvarchar(100) NOT NULL,
    [LastName] nvarchar(100) NOT NULL,
    [CompanyName] nvarchar(100) NULL,
    [Email] nvarchar(255) NOT NULL,
    [PrimaryPhone] nvarchar(20) NOT NULL,
    [SecondaryPhone] nvarchar(20) NULL,
    [BillingAddress] nvarchar(500) NOT NULL,
    [BillingCity] nvarchar(100) NOT NULL,
    [BillingState] nvarchar(100) NOT NULL,
    [BillingZipCode] nvarchar(20) NOT NULL,
    [BillingCountry] nvarchar(100) NOT NULL,
    [InstallationAddress] nvarchar(500) NOT NULL,
    [InstallationCity] nvarchar(100) NOT NULL,
    [InstallationState] nvarchar(100) NOT NULL,
    [InstallationZipCode] nvarchar(20) NOT NULL,
    [InstallationCountry] nvarchar(100) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [Notes] nvarchar(1000) NULL,
    CONSTRAINT [PK_Customers] PRIMARY KEY ([CustomerId])
);
2025-09-09 15:03:44.975 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Devices] (
    [DeviceId] int NOT NULL IDENTITY,
    [DeviceName] nvarchar(100) NOT NULL,
    [DeviceType] int NOT NULL,
    [Model] nvarchar(100) NOT NULL,
    [Manufacturer] nvarchar(100) NOT NULL,
    [Description] nvarchar(1000) NULL,
    [Specifications] nvarchar(1000) NULL,
    [UnitPrice] decimal(10,2) NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_Devices] PRIMARY KEY ([DeviceId])
);
2025-09-09 15:03:44.980 +05:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Roles] (
    [Id] nvarchar(450) NOT NULL,
    [Name] nvarchar(256) NULL,
    [NormalizedName] nvarchar(256) NULL,
    [ConcurrencyStamp] nvarchar(max) NULL,
    CONSTRAINT [PK_Roles] PRIMARY KEY ([Id])
);
2025-09-09 15:03:44.985 +05:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [SIMCards] (
    [SIMCardId] int NOT NULL IDENTITY,
    [SIMNumber] nvarchar(50) NOT NULL,
    [ICCID] nvarchar(50) NOT NULL,
    [Carrier] nvarchar(100) NULL,
    [PlanType] nvarchar(50) NULL,
    [MonthlyFee] decimal(8,2) NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_SIMCards] PRIMARY KEY ([SIMCardId])
);
2025-09-09 15:03:44.991 +05:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Users] (
    [Id] nvarchar(450) NOT NULL,
    [FirstName] nvarchar(100) NOT NULL,
    [LastName] nvarchar(100) NOT NULL,
    [Role] int NOT NULL,
    [EmployeeId] nvarchar(15) NULL,
    [Department] nvarchar(200) NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [LastLoginAt] datetime2 NULL,
    [Notes] nvarchar(500) NULL,
    [UserName] nvarchar(256) NULL,
    [NormalizedUserName] nvarchar(256) NULL,
    [Email] nvarchar(256) NULL,
    [NormalizedEmail] nvarchar(256) NULL,
    [EmailConfirmed] bit NOT NULL,
    [PasswordHash] nvarchar(max) NULL,
    [SecurityStamp] nvarchar(max) NULL,
    [ConcurrencyStamp] nvarchar(max) NULL,
    [PhoneNumber] nvarchar(max) NULL,
    [PhoneNumberConfirmed] bit NOT NULL,
    [TwoFactorEnabled] bit NOT NULL,
    [LockoutEnd] datetimeoffset NULL,
    [LockoutEnabled] bit NOT NULL,
    [AccessFailedCount] int NOT NULL,
    CONSTRAINT [PK_Users] PRIMARY KEY ([Id])
);
2025-09-09 15:03:45.000 +05:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [RoleClaims] (
    [Id] int NOT NULL IDENTITY,
    [RoleId] nvarchar(450) NOT NULL,
    [ClaimType] nvarchar(max) NULL,
    [ClaimValue] nvarchar(max) NULL,
    CONSTRAINT [PK_RoleClaims] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_RoleClaims_Roles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [Roles] ([Id]) ON DELETE CASCADE
);
2025-09-09 15:03:45.144 +05:00 [INF] Executed DbCommand (143ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [InventoryAlerts] (
    [InventoryAlertId] int NOT NULL IDENTITY,
    [DeviceId] int NULL,
    [SIMCardId] int NULL,
    [CurrentStock] int NOT NULL,
    [ThresholdLevel] int NOT NULL,
    [IsProcessed] bit NOT NULL,
    [AlertDate] datetime2 NOT NULL,
    [ProcessedAt] datetime2 NULL,
    [Notes] nvarchar(1000) NULL,
    CONSTRAINT [PK_InventoryAlerts] PRIMARY KEY ([InventoryAlertId]),
    CONSTRAINT [FK_InventoryAlerts_Devices_DeviceId] FOREIGN KEY ([DeviceId]) REFERENCES [Devices] ([DeviceId]) ON DELETE CASCADE,
    CONSTRAINT [FK_InventoryAlerts_SIMCards_SIMCardId] FOREIGN KEY ([SIMCardId]) REFERENCES [SIMCards] ([SIMCardId]) ON DELETE CASCADE
);
2025-09-09 15:03:45.188 +05:00 [INF] Executed DbCommand (42ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [AuditLogs] (
    [AuditLogId] int NOT NULL IDENTITY,
    [UserId] nvarchar(450) NOT NULL,
    [Action] nvarchar(100) NOT NULL,
    [EntityType] nvarchar(100) NOT NULL,
    [EntityId] int NULL,
    [OldValues] nvarchar(2000) NULL,
    [NewValues] nvarchar(2000) NULL,
    [Description] nvarchar(500) NULL,
    [IPAddress] nvarchar(45) NULL,
    [UserAgent] nvarchar(500) NULL,
    [Timestamp] datetime2 NOT NULL,
    CONSTRAINT [PK_AuditLogs] PRIMARY KEY ([AuditLogId]),
    CONSTRAINT [FK_AuditLogs_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION
);
2025-09-09 15:03:45.204 +05:00 [INF] Executed DbCommand (13ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [InventoryItems] (
    [InventoryItemId] int NOT NULL IDENTITY,
    [DeviceId] int NULL,
    [SIMCardId] int NULL,
    [SerialNumber] nvarchar(100) NOT NULL,
    [Status] int NOT NULL,
    [DeadStockCategory] int NULL,
    [DeadStockReason] nvarchar(500) NULL,
    [CurrentLocation] nvarchar(200) NULL,
    [AssignedToUserId] nvarchar(450) NULL,
    [AssignedToCustomerId] int NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [Notes] nvarchar(1000) NULL,
    CONSTRAINT [PK_InventoryItems] PRIMARY KEY ([InventoryItemId]),
    CONSTRAINT [FK_InventoryItems_Customers_AssignedToCustomerId] FOREIGN KEY ([AssignedToCustomerId]) REFERENCES [Customers] ([CustomerId]) ON DELETE SET NULL,
    CONSTRAINT [FK_InventoryItems_Devices_DeviceId] FOREIGN KEY ([DeviceId]) REFERENCES [Devices] ([DeviceId]) ON DELETE NO ACTION,
    CONSTRAINT [FK_InventoryItems_SIMCards_SIMCardId] FOREIGN KEY ([SIMCardId]) REFERENCES [SIMCards] ([SIMCardId]) ON DELETE NO ACTION,
    CONSTRAINT [FK_InventoryItems_Users_AssignedToUserId] FOREIGN KEY ([AssignedToUserId]) REFERENCES [Users] ([Id]) ON DELETE SET NULL
);
2025-09-09 15:03:45.213 +05:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Notifications] (
    [NotificationId] int NOT NULL IDENTITY,
    [UserId] nvarchar(450) NOT NULL,
    [Type] int NOT NULL,
    [Title] nvarchar(200) NOT NULL,
    [Message] nvarchar(1000) NOT NULL,
    [IsRead] bit NOT NULL,
    [IsEmailSent] bit NOT NULL,
    [IsSMSSent] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [ReadAt] datetime2 NULL,
    [EmailSentAt] datetime2 NULL,
    [SMSSentAt] datetime2 NULL,
    [ActionUrl] nvarchar(500) NULL,
    [AdditionalData] nvarchar(1000) NULL,
    CONSTRAINT [PK_Notifications] PRIMARY KEY ([NotificationId]),
    CONSTRAINT [FK_Notifications_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
2025-09-09 15:03:45.220 +05:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Sales] (
    [SaleId] int NOT NULL IDENTITY,
    [SaleNumber] nvarchar(20) NOT NULL,
    [CustomerId] int NOT NULL,
    [SalespersonId] nvarchar(450) NOT NULL,
    [TotalAmount] decimal(12,2) NOT NULL,
    [DiscountAmount] decimal(12,2) NULL,
    [FinalAmount] decimal(12,2) NOT NULL,
    [PaymentTerms] nvarchar(100) NULL,
    [AnnualRenewalAmount] decimal(10,2) NOT NULL,
    [RenewalDate] datetime2 NOT NULL,
    [AutoRenewal] bit NOT NULL,
    [SaleDate] datetime2 NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [Notes] nvarchar(1000) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_Sales] PRIMARY KEY ([SaleId]),
    CONSTRAINT [FK_Sales_Customers_CustomerId] FOREIGN KEY ([CustomerId]) REFERENCES [Customers] ([CustomerId]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Sales_Users_SalespersonId] FOREIGN KEY ([SalespersonId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION
);
2025-09-09 15:03:45.233 +05:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UserClaims] (
    [Id] int NOT NULL IDENTITY,
    [UserId] nvarchar(450) NOT NULL,
    [ClaimType] nvarchar(max) NULL,
    [ClaimValue] nvarchar(max) NULL,
    CONSTRAINT [PK_UserClaims] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_UserClaims_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
2025-09-09 15:03:45.249 +05:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UserLogins] (
    [LoginProvider] nvarchar(450) NOT NULL,
    [ProviderKey] nvarchar(450) NOT NULL,
    [ProviderDisplayName] nvarchar(max) NULL,
    [UserId] nvarchar(450) NOT NULL,
    CONSTRAINT [PK_UserLogins] PRIMARY KEY ([LoginProvider], [ProviderKey]),
    CONSTRAINT [FK_UserLogins_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
2025-09-09 15:03:45.259 +05:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UserRoles] (
    [UserId] nvarchar(450) NOT NULL,
    [RoleId] nvarchar(450) NOT NULL,
    CONSTRAINT [PK_UserRoles] PRIMARY KEY ([UserId], [RoleId]),
    CONSTRAINT [FK_UserRoles_Roles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [Roles] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_UserRoles_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
2025-09-09 15:03:45.269 +05:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [UserTokens] (
    [UserId] nvarchar(450) NOT NULL,
    [LoginProvider] nvarchar(450) NOT NULL,
    [Name] nvarchar(450) NOT NULL,
    [Value] nvarchar(max) NULL,
    CONSTRAINT [PK_UserTokens] PRIMARY KEY ([UserId], [LoginProvider], [Name]),
    CONSTRAINT [FK_UserTokens_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
2025-09-09 15:03:45.280 +05:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [InventoryAssignments] (
    [AssignmentId] int NOT NULL IDENTITY,
    [InventoryItemId] int NOT NULL,
    [AssignedToUserId] nvarchar(450) NOT NULL,
    [AssignedByUserId] nvarchar(450) NOT NULL,
    [AssignedAt] datetime2 NOT NULL,
    [ReturnedAt] datetime2 NULL,
    [AssignmentReason] nvarchar(500) NULL,
    [ReturnReason] nvarchar(500) NULL,
    [Notes] nvarchar(1000) NULL,
    [IsActive] bit NOT NULL,
    CONSTRAINT [PK_InventoryAssignments] PRIMARY KEY ([AssignmentId]),
    CONSTRAINT [FK_InventoryAssignments_InventoryItems_InventoryItemId] FOREIGN KEY ([InventoryItemId]) REFERENCES [InventoryItems] ([InventoryItemId]) ON DELETE CASCADE,
    CONSTRAINT [FK_InventoryAssignments_Users_AssignedByUserId] FOREIGN KEY ([AssignedByUserId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_InventoryAssignments_Users_AssignedToUserId] FOREIGN KEY ([AssignedToUserId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION
);
2025-09-09 15:03:45.294 +05:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [StockTransfers] (
    [TransferId] int NOT NULL IDENTITY,
    [InventoryItemId] int NOT NULL,
    [FromUserId] nvarchar(450) NULL,
    [ToUserId] nvarchar(450) NULL,
    [FromCustomerId] int NULL,
    [ToCustomerId] int NULL,
    [FromLocation] nvarchar(200) NULL,
    [ToLocation] nvarchar(200) NULL,
    [Reason] int NOT NULL,
    [InitiatedByUserId] nvarchar(450) NOT NULL,
    [TransferDate] datetime2 NOT NULL,
    [Notes] nvarchar(1000) NULL,
    [InventoryItemId1] int NULL,
    CONSTRAINT [PK_StockTransfers] PRIMARY KEY ([TransferId]),
    CONSTRAINT [FK_StockTransfers_Customers_FromCustomerId] FOREIGN KEY ([FromCustomerId]) REFERENCES [Customers] ([CustomerId]) ON DELETE NO ACTION,
    CONSTRAINT [FK_StockTransfers_Customers_ToCustomerId] FOREIGN KEY ([ToCustomerId]) REFERENCES [Customers] ([CustomerId]) ON DELETE NO ACTION,
    CONSTRAINT [FK_StockTransfers_InventoryItems_InventoryItemId] FOREIGN KEY ([InventoryItemId]) REFERENCES [InventoryItems] ([InventoryItemId]) ON DELETE CASCADE,
    CONSTRAINT [FK_StockTransfers_InventoryItems_InventoryItemId1] FOREIGN KEY ([InventoryItemId1]) REFERENCES [InventoryItems] ([InventoryItemId]),
    CONSTRAINT [FK_StockTransfers_Users_FromUserId] FOREIGN KEY ([FromUserId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_StockTransfers_Users_InitiatedByUserId] FOREIGN KEY ([InitiatedByUserId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_StockTransfers_Users_ToUserId] FOREIGN KEY ([ToUserId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION
);
2025-09-09 15:03:45.308 +05:00 [INF] Executed DbCommand (11ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Jobs] (
    [JobId] int NOT NULL IDENTITY,
    [JobNumber] nvarchar(20) NOT NULL,
    [SaleId] int NOT NULL,
    [AssignedToUserId] nvarchar(450) NOT NULL,
    [Status] int NOT NULL,
    [Priority] int NOT NULL,
    [Title] nvarchar(200) NOT NULL,
    [Description] nvarchar(2000) NULL,
    [InstallationInstructions] nvarchar(2000) NULL,
    [ScheduledDate] datetime2 NOT NULL,
    [StartedAt] datetime2 NULL,
    [CompletedAt] datetime2 NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [CompletionNotes] nvarchar(2000) NULL,
    [IssuesEncountered] nvarchar(2000) NULL,
    CONSTRAINT [PK_Jobs] PRIMARY KEY ([JobId]),
    CONSTRAINT [FK_Jobs_Sales_SaleId] FOREIGN KEY ([SaleId]) REFERENCES [Sales] ([SaleId]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Jobs_Users_AssignedToUserId] FOREIGN KEY ([AssignedToUserId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION
);
2025-09-09 15:03:45.321 +05:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [Queries] (
    [QueryId] int NOT NULL IDENTITY,
    [QueryNumber] nvarchar(20) NOT NULL,
    [SaleId] int NOT NULL,
    [CreatedByUserId] nvarchar(450) NOT NULL,
    [ProcessedByUserId] nvarchar(450) NULL,
    [Status] int NOT NULL,
    [Priority] int NOT NULL,
    [Subject] nvarchar(200) NOT NULL,
    [Description] nvarchar(2000) NOT NULL,
    [Response] nvarchar(2000) NULL,
    [InternalNotes] nvarchar(2000) NULL,
    [CreatedAt] datetime2 NOT NULL,
    [ProcessedAt] datetime2 NULL,
    [UpdatedAt] datetime2 NOT NULL,
    [DueDate] datetime2 NULL,
    CONSTRAINT [PK_Queries] PRIMARY KEY ([QueryId]),
    CONSTRAINT [FK_Queries_Sales_SaleId] FOREIGN KEY ([SaleId]) REFERENCES [Sales] ([SaleId]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Queries_Users_CreatedByUserId] FOREIGN KEY ([CreatedByUserId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION,
    CONSTRAINT [FK_Queries_Users_ProcessedByUserId] FOREIGN KEY ([ProcessedByUserId]) REFERENCES [Users] ([Id]) ON DELETE SET NULL
);
2025-09-09 15:03:45.406 +05:00 [INF] Executed DbCommand (80ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [RenewalAlerts] (
    [RenewalAlertId] int NOT NULL IDENTITY,
    [SaleId] int NOT NULL,
    [DaysBeforeRenewal] int NOT NULL,
    [IsProcessed] bit NOT NULL,
    [AlertDate] datetime2 NOT NULL,
    [ProcessedAt] datetime2 NULL,
    [Notes] nvarchar(1000) NULL,
    CONSTRAINT [PK_RenewalAlerts] PRIMARY KEY ([RenewalAlertId]),
    CONSTRAINT [FK_RenewalAlerts_Sales_SaleId] FOREIGN KEY ([SaleId]) REFERENCES [Sales] ([SaleId]) ON DELETE CASCADE
);
2025-09-09 15:03:45.424 +05:00 [INF] Executed DbCommand (15ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [SaleItems] (
    [SaleItemId] int NOT NULL IDENTITY,
    [SaleId] int NOT NULL,
    [DeviceId] int NULL,
    [SIMCardId] int NULL,
    [Quantity] int NOT NULL,
    [UnitPrice] decimal(10,2) NOT NULL,
    [TotalPrice] decimal(12,2) NOT NULL,
    [SerialNumbers] nvarchar(500) NULL,
    [Notes] nvarchar(1000) NULL,
    CONSTRAINT [PK_SaleItems] PRIMARY KEY ([SaleItemId]),
    CONSTRAINT [FK_SaleItems_Devices_DeviceId] FOREIGN KEY ([DeviceId]) REFERENCES [Devices] ([DeviceId]) ON DELETE NO ACTION,
    CONSTRAINT [FK_SaleItems_SIMCards_SIMCardId] FOREIGN KEY ([SIMCardId]) REFERENCES [SIMCards] ([SIMCardId]) ON DELETE NO ACTION,
    CONSTRAINT [FK_SaleItems_Sales_SaleId] FOREIGN KEY ([SaleId]) REFERENCES [Sales] ([SaleId]) ON DELETE CASCADE
);
2025-09-09 15:03:45.434 +05:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [JobPhotos] (
    [PhotoId] int NOT NULL IDENTITY,
    [JobId] int NOT NULL,
    [FileName] nvarchar(255) NOT NULL,
    [FilePath] nvarchar(500) NOT NULL,
    [Caption] nvarchar(500) NULL,
    [PhotoType] nvarchar(100) NULL,
    [TakenAt] datetime2 NOT NULL,
    [UploadedByUserId] nvarchar(450) NOT NULL,
    CONSTRAINT [PK_JobPhotos] PRIMARY KEY ([PhotoId]),
    CONSTRAINT [FK_JobPhotos_Jobs_JobId] FOREIGN KEY ([JobId]) REFERENCES [Jobs] ([JobId]) ON DELETE CASCADE,
    CONSTRAINT [FK_JobPhotos_Users_UploadedByUserId] FOREIGN KEY ([UploadedByUserId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION
);
2025-09-09 15:03:45.443 +05:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [JobUpdates] (
    [UpdateId] int NOT NULL IDENTITY,
    [JobId] int NOT NULL,
    [UserId] nvarchar(450) NOT NULL,
    [PreviousStatus] int NOT NULL,
    [NewStatus] int NOT NULL,
    [UpdateNotes] nvarchar(1000) NOT NULL,
    [UpdatedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_JobUpdates] PRIMARY KEY ([UpdateId]),
    CONSTRAINT [FK_JobUpdates_Jobs_JobId] FOREIGN KEY ([JobId]) REFERENCES [Jobs] ([JobId]) ON DELETE CASCADE,
    CONSTRAINT [FK_JobUpdates_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION
);
2025-09-09 15:03:45.449 +05:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [QueryAttachments] (
    [AttachmentId] int NOT NULL IDENTITY,
    [QueryId] int NOT NULL,
    [FileName] nvarchar(255) NOT NULL,
    [FilePath] nvarchar(500) NOT NULL,
    [ContentType] nvarchar(100) NULL,
    [FileSize] bigint NOT NULL,
    [UploadedByUserId] nvarchar(450) NOT NULL,
    [UploadedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_QueryAttachments] PRIMARY KEY ([AttachmentId]),
    CONSTRAINT [FK_QueryAttachments_Queries_QueryId] FOREIGN KEY ([QueryId]) REFERENCES [Queries] ([QueryId]) ON DELETE CASCADE,
    CONSTRAINT [FK_QueryAttachments_Users_UploadedByUserId] FOREIGN KEY ([UploadedByUserId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION
);
2025-09-09 15:03:45.457 +05:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE [QueryComments] (
    [CommentId] int NOT NULL IDENTITY,
    [QueryId] int NOT NULL,
    [UserId] nvarchar(450) NOT NULL,
    [Comment] nvarchar(2000) NOT NULL,
    [IsInternal] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_QueryComments] PRIMARY KEY ([CommentId]),
    CONSTRAINT [FK_QueryComments_Queries_QueryId] FOREIGN KEY ([QueryId]) REFERENCES [Queries] ([QueryId]) ON DELETE CASCADE,
    CONSTRAINT [FK_QueryComments_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE NO ACTION
);
2025-09-09 15:03:45.606 +05:00 [INF] Executed DbCommand (147ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'DeviceId', N'CreatedAt', N'Description', N'DeviceName', N'DeviceType', N'IsActive', N'Manufacturer', N'Model', N'Specifications', N'UnitPrice', N'UpdatedAt') AND [object_id] = OBJECT_ID(N'[Devices]'))
    SET IDENTITY_INSERT [Devices] ON;
INSERT INTO [Devices] ([DeviceId], [CreatedAt], [Description], [DeviceName], [DeviceType], [IsActive], [Manufacturer], [Model], [Specifications], [UnitPrice], [UpdatedAt])
VALUES (1, '2025-09-09T10:03:28.6285227Z', N'Professional GPS tracking device with real-time monitoring', N'GPS Tracker Pro', 1, CAST(1 AS bit), N'TrackTech', N'GT-2000', NULL, 299.99, '2025-09-09T10:03:28.6285227Z'),
(2, '2025-09-09T10:03:28.6285230Z', N'High-definition security camera with night vision', N'Security Camera HD', 2, CAST(1 AS bit), N'SecureTech', N'SC-1080', NULL, 199.99, '2025-09-09T10:03:28.6285230Z');
IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'DeviceId', N'CreatedAt', N'Description', N'DeviceName', N'DeviceType', N'IsActive', N'Manufacturer', N'Model', N'Specifications', N'UnitPrice', N'UpdatedAt') AND [object_id] = OBJECT_ID(N'[Devices]'))
    SET IDENTITY_INSERT [Devices] OFF;
2025-09-09 15:03:45.636 +05:00 [INF] Executed DbCommand (26ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'Id', N'ConcurrencyStamp', N'Name', N'NormalizedName') AND [object_id] = OBJECT_ID(N'[Roles]'))
    SET IDENTITY_INSERT [Roles] ON;
INSERT INTO [Roles] ([Id], [ConcurrencyStamp], [Name], [NormalizedName])
VALUES (N'1', NULL, N'Salesperson', N'SALESPERSON'),
(N'2', NULL, N'OperationsStaff', N'OPERATIONSSTAFF'),
(N'3', NULL, N'Technician', N'TECHNICIAN'),
(N'4', NULL, N'Management', N'MANAGEMENT');
IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'Id', N'ConcurrencyStamp', N'Name', N'NormalizedName') AND [object_id] = OBJECT_ID(N'[Roles]'))
    SET IDENTITY_INSERT [Roles] OFF;
2025-09-09 15:03:45.677 +05:00 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'SIMCardId', N'Carrier', N'CreatedAt', N'ICCID', N'IsActive', N'MonthlyFee', N'PlanType', N'SIMNumber', N'UpdatedAt') AND [object_id] = OBJECT_ID(N'[SIMCards]'))
    SET IDENTITY_INSERT [SIMCards] ON;
INSERT INTO [SIMCards] ([SIMCardId], [Carrier], [CreatedAt], [ICCID], [IsActive], [MonthlyFee], [PlanType], [SIMNumber], [UpdatedAt])
VALUES (1, N'Verizon', '2025-09-09T10:03:28.6285297Z', N'89012345678901234567', CAST(1 AS bit), 15.0, N'IoT Data Plan', N'1234567890', '2025-09-09T10:03:28.6285298Z'),
(2, N'AT&T', '2025-09-09T10:03:28.6285300Z', N'89012345678901234568', CAST(1 AS bit), 12.0, N'IoT Data Plan', N'0987654321', '2025-09-09T10:03:28.6285300Z');
IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'SIMCardId', N'Carrier', N'CreatedAt', N'ICCID', N'IsActive', N'MonthlyFee', N'PlanType', N'SIMNumber', N'UpdatedAt') AND [object_id] = OBJECT_ID(N'[SIMCards]'))
    SET IDENTITY_INSERT [SIMCards] OFF;
2025-09-09 15:03:45.699 +05:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_AuditLogs_UserId] ON [AuditLogs] ([UserId]);
2025-09-09 15:03:45.705 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Customers_Email] ON [Customers] ([Email]);
2025-09-09 15:03:45.713 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryAlerts_DeviceId] ON [InventoryAlerts] ([DeviceId]);
2025-09-09 15:03:45.715 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryAlerts_SIMCardId] ON [InventoryAlerts] ([SIMCardId]);
2025-09-09 15:03:45.719 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryAssignments_AssignedByUserId] ON [InventoryAssignments] ([AssignedByUserId]);
2025-09-09 15:03:45.724 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryAssignments_AssignedToUserId] ON [InventoryAssignments] ([AssignedToUserId]);
2025-09-09 15:03:45.727 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryAssignments_InventoryItemId] ON [InventoryAssignments] ([InventoryItemId]);
2025-09-09 15:03:45.731 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryItems_AssignedToCustomerId] ON [InventoryItems] ([AssignedToCustomerId]);
2025-09-09 15:03:45.733 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryItems_AssignedToUserId] ON [InventoryItems] ([AssignedToUserId]);
2025-09-09 15:03:45.735 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryItems_DeviceId] ON [InventoryItems] ([DeviceId]);
2025-09-09 15:03:45.739 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_InventoryItems_SerialNumber] ON [InventoryItems] ([SerialNumber]);
2025-09-09 15:03:45.743 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryItems_SIMCardId] ON [InventoryItems] ([SIMCardId]);
2025-09-09 15:03:45.746 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_InventoryItems_Status] ON [InventoryItems] ([Status]);
2025-09-09 15:03:45.750 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_JobPhotos_JobId] ON [JobPhotos] ([JobId]);
2025-09-09 15:03:45.753 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_JobPhotos_UploadedByUserId] ON [JobPhotos] ([UploadedByUserId]);
2025-09-09 15:03:45.762 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Jobs_AssignedToUserId] ON [Jobs] ([AssignedToUserId]);
2025-09-09 15:03:45.765 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Jobs_JobNumber] ON [Jobs] ([JobNumber]);
2025-09-09 15:03:45.767 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Jobs_SaleId] ON [Jobs] ([SaleId]);
2025-09-09 15:03:45.771 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Jobs_ScheduledDate] ON [Jobs] ([ScheduledDate]);
2025-09-09 15:03:45.778 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Jobs_Status] ON [Jobs] ([Status]);
2025-09-09 15:03:45.783 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_JobUpdates_JobId] ON [JobUpdates] ([JobId]);
2025-09-09 15:03:45.791 +05:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_JobUpdates_UserId] ON [JobUpdates] ([UserId]);
2025-09-09 15:03:45.798 +05:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Notifications_CreatedAt] ON [Notifications] ([CreatedAt]);
2025-09-09 15:03:45.801 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Notifications_UserId_IsRead] ON [Notifications] ([UserId], [IsRead]);
2025-09-09 15:03:45.805 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Queries_CreatedByUserId] ON [Queries] ([CreatedByUserId]);
2025-09-09 15:03:45.811 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Queries_Priority] ON [Queries] ([Priority]);
2025-09-09 15:03:45.814 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Queries_ProcessedByUserId] ON [Queries] ([ProcessedByUserId]);
2025-09-09 15:03:45.820 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Queries_QueryNumber] ON [Queries] ([QueryNumber]);
2025-09-09 15:03:45.827 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Queries_SaleId] ON [Queries] ([SaleId]);
2025-09-09 15:03:45.832 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Queries_Status] ON [Queries] ([Status]);
2025-09-09 15:03:45.834 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_QueryAttachments_QueryId] ON [QueryAttachments] ([QueryId]);
2025-09-09 15:03:45.836 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_QueryAttachments_UploadedByUserId] ON [QueryAttachments] ([UploadedByUserId]);
2025-09-09 15:03:45.840 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_QueryComments_QueryId] ON [QueryComments] ([QueryId]);
2025-09-09 15:03:45.843 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_QueryComments_UserId] ON [QueryComments] ([UserId]);
2025-09-09 15:03:45.846 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_RenewalAlerts_SaleId] ON [RenewalAlerts] ([SaleId]);
2025-09-09 15:03:45.849 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_RoleClaims_RoleId] ON [RoleClaims] ([RoleId]);
2025-09-09 15:03:45.927 +05:00 [INF] Executed DbCommand (76ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [RoleNameIndex] ON [Roles] ([NormalizedName]) WHERE [NormalizedName] IS NOT NULL;
2025-09-09 15:03:45.930 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_SaleItems_DeviceId] ON [SaleItems] ([DeviceId]);
2025-09-09 15:03:45.937 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_SaleItems_SaleId] ON [SaleItems] ([SaleId]);
2025-09-09 15:03:45.945 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_SaleItems_SIMCardId] ON [SaleItems] ([SIMCardId]);
2025-09-09 15:03:45.948 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Sales_CustomerId] ON [Sales] ([CustomerId]);
2025-09-09 15:03:45.952 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Sales_RenewalDate] ON [Sales] ([RenewalDate]);
2025-09-09 15:03:45.956 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Sales_SaleNumber] ON [Sales] ([SaleNumber]);
2025-09-09 15:03:45.959 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Sales_SalespersonId] ON [Sales] ([SalespersonId]);
2025-09-09 15:03:45.962 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_StockTransfers_FromCustomerId] ON [StockTransfers] ([FromCustomerId]);
2025-09-09 15:03:45.966 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_StockTransfers_FromUserId] ON [StockTransfers] ([FromUserId]);
2025-09-09 15:03:45.968 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_StockTransfers_InitiatedByUserId] ON [StockTransfers] ([InitiatedByUserId]);
2025-09-09 15:03:45.971 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_StockTransfers_InventoryItemId] ON [StockTransfers] ([InventoryItemId]);
2025-09-09 15:03:45.976 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_StockTransfers_InventoryItemId1] ON [StockTransfers] ([InventoryItemId1]);
2025-09-09 15:03:45.979 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_StockTransfers_ToCustomerId] ON [StockTransfers] ([ToCustomerId]);
2025-09-09 15:03:45.982 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_StockTransfers_ToUserId] ON [StockTransfers] ([ToUserId]);
2025-09-09 15:03:45.985 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_UserClaims_UserId] ON [UserClaims] ([UserId]);
2025-09-09 15:03:45.990 +05:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_UserLogins_UserId] ON [UserLogins] ([UserId]);
2025-09-09 15:03:45.997 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_UserRoles_RoleId] ON [UserRoles] ([RoleId]);
2025-09-09 15:03:46.002 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [EmailIndex] ON [Users] ([NormalizedEmail]);
2025-09-09 15:03:46.008 +05:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [IX_Users_EmployeeId] ON [Users] ([EmployeeId]) WHERE [EmployeeId] IS NOT NULL;
2025-09-09 15:03:46.013 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE INDEX [IX_Users_Role] ON [Users] ([Role]);
2025-09-09 15:03:46.017 +05:00 [INF] Executed DbCommand (2ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE UNIQUE INDEX [UserNameIndex] ON [Users] ([NormalizedUserName]) WHERE [NormalizedUserName] IS NOT NULL;
2025-09-09 15:03:46.043 +05:00 [INF] Executed DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250909100329_InitialCreate', N'8.0.0');
2025-09-09 15:10:45.581 +05:00 [INF] User profile is available. Using 'C:\Users\<USER>\AppData\Local\ASP.NET\DataProtection-Keys' as key repository and Windows DPAPI to encrypt keys at rest.
2025-09-09 15:10:46.103 +05:00 [WRN] The foreign key property 'StockTransfer.InventoryItemId1' was created in shadow state because a conflicting property with the simple name 'InventoryItemId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-09-09 15:10:46.564 +05:00 [INF] Executed DbCommand (17ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-09-09 15:10:46.596 +05:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-09-09 15:10:46.603 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-09-09 15:10:46.611 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-09-09 15:10:46.626 +05:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-09-09 15:10:46.639 +05:00 [INF] No migrations were applied. The database is already up to date.
2025-09-09 15:10:46.820 +05:00 [INF] Executed DbCommand (26ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[ConcurrencyStamp], [r].[Name], [r].[NormalizedName]
FROM [Roles] AS [r]
WHERE [r].[NormalizedName] = @__normalizedName_0
2025-09-09 15:10:46.846 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[ConcurrencyStamp], [r].[Name], [r].[NormalizedName]
FROM [Roles] AS [r]
WHERE [r].[NormalizedName] = @__normalizedName_0
2025-09-09 15:10:46.850 +05:00 [INF] Executed DbCommand (0ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[ConcurrencyStamp], [r].[Name], [r].[NormalizedName]
FROM [Roles] AS [r]
WHERE [r].[NormalizedName] = @__normalizedName_0
2025-09-09 15:10:46.852 +05:00 [INF] Executed DbCommand (0ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[ConcurrencyStamp], [r].[Name], [r].[NormalizedName]
FROM [Roles] AS [r]
WHERE [r].[NormalizedName] = @__normalizedName_0
2025-09-09 15:10:46.869 +05:00 [INF] Executed DbCommand (6ms) [Parameters=[@__normalizedEmail_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [u].[Id], [u].[AccessFailedCount], [u].[ConcurrencyStamp], [u].[CreatedAt], [u].[Department], [u].[Email], [u].[EmailConfirmed], [u].[EmployeeId], [u].[FirstName], [u].[IsActive], [u].[LastLoginAt], [u].[LastName], [u].[LockoutEnabled], [u].[LockoutEnd], [u].[NormalizedEmail], [u].[NormalizedUserName], [u].[Notes], [u].[PasswordHash], [u].[PhoneNumber], [u].[PhoneNumberConfirmed], [u].[Role], [u].[SecurityStamp], [u].[TwoFactorEnabled], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[NormalizedEmail] = @__normalizedEmail_0
2025-09-09 15:10:46.938 +05:00 [INF] Executed DbCommand (5ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[AccessFailedCount], [u].[ConcurrencyStamp], [u].[CreatedAt], [u].[Department], [u].[Email], [u].[EmailConfirmed], [u].[EmployeeId], [u].[FirstName], [u].[IsActive], [u].[LastLoginAt], [u].[LastName], [u].[LockoutEnabled], [u].[LockoutEnd], [u].[NormalizedEmail], [u].[NormalizedUserName], [u].[Notes], [u].[PasswordHash], [u].[PhoneNumber], [u].[PhoneNumberConfirmed], [u].[Role], [u].[SecurityStamp], [u].[TwoFactorEnabled], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[NormalizedUserName] = @__normalizedUserName_0
2025-09-09 15:10:46.944 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedEmail_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [u].[Id], [u].[AccessFailedCount], [u].[ConcurrencyStamp], [u].[CreatedAt], [u].[Department], [u].[Email], [u].[EmailConfirmed], [u].[EmployeeId], [u].[FirstName], [u].[IsActive], [u].[LastLoginAt], [u].[LastName], [u].[LockoutEnabled], [u].[LockoutEnd], [u].[NormalizedEmail], [u].[NormalizedUserName], [u].[Notes], [u].[PasswordHash], [u].[PhoneNumber], [u].[PhoneNumberConfirmed], [u].[Role], [u].[SecurityStamp], [u].[TwoFactorEnabled], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[NormalizedEmail] = @__normalizedEmail_0
2025-09-09 15:10:47.060 +05:00 [INF] Executed DbCommand (18ms) [Parameters=[@p0='?' (Size = 450), @p1='?' (DbType = Int32), @p2='?' (Size = 4000), @p3='?' (DbType = DateTime2), @p4='?' (Size = 200), @p5='?' (Size = 256), @p6='?' (DbType = Boolean), @p7='?' (Size = 15), @p8='?' (Size = 100), @p9='?' (DbType = Boolean), @p10='?' (DbType = DateTime2), @p11='?' (Size = 100), @p12='?' (DbType = Boolean), @p13='?' (DbType = DateTimeOffset), @p14='?' (Size = 256), @p15='?' (Size = 256), @p16='?' (Size = 500), @p17='?' (Size = 4000), @p18='?' (Size = 4000), @p19='?' (DbType = Boolean), @p20='?' (DbType = Int32), @p21='?' (Size = 4000), @p22='?' (DbType = Boolean), @p23='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
INSERT INTO [Users] ([Id], [AccessFailedCount], [ConcurrencyStamp], [CreatedAt], [Department], [Email], [EmailConfirmed], [EmployeeId], [FirstName], [IsActive], [LastLoginAt], [LastName], [LockoutEnabled], [LockoutEnd], [NormalizedEmail], [NormalizedUserName], [Notes], [PasswordHash], [PhoneNumber], [PhoneNumberConfirmed], [Role], [SecurityStamp], [TwoFactorEnabled], [UserName])
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23);
2025-09-09 15:10:47.078 +05:00 [INF] Executed DbCommand (5ms) [Parameters=[@__normalizedRoleName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [r].[Id], [r].[ConcurrencyStamp], [r].[Name], [r].[NormalizedName]
FROM [Roles] AS [r]
WHERE [r].[NormalizedName] = @__normalizedRoleName_0
2025-09-09 15:10:47.106 +05:00 [INF] Executed DbCommand (5ms) [Parameters=[@__p_0='?' (Size = 450), @__p_1='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[UserId], [u].[RoleId]
FROM [UserRoles] AS [u]
WHERE [u].[UserId] = @__p_0 AND [u].[RoleId] = @__p_1
2025-09-09 15:10:47.114 +05:00 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedRoleName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [r].[Id], [r].[ConcurrencyStamp], [r].[Name], [r].[NormalizedName]
FROM [Roles] AS [r]
WHERE [r].[NormalizedName] = @__normalizedRoleName_0
2025-09-09 15:10:47.148 +05:00 [INF] Executed DbCommand (24ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[AccessFailedCount], [u].[ConcurrencyStamp], [u].[CreatedAt], [u].[Department], [u].[Email], [u].[EmailConfirmed], [u].[EmployeeId], [u].[FirstName], [u].[IsActive], [u].[LastLoginAt], [u].[LastName], [u].[LockoutEnabled], [u].[LockoutEnd], [u].[NormalizedEmail], [u].[NormalizedUserName], [u].[Notes], [u].[PasswordHash], [u].[PhoneNumber], [u].[PhoneNumberConfirmed], [u].[Role], [u].[SecurityStamp], [u].[TwoFactorEnabled], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[NormalizedUserName] = @__normalizedUserName_0
2025-09-09 15:10:47.166 +05:00 [INF] Executed DbCommand (7ms) [Parameters=[@__normalizedEmail_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [u].[Id], [u].[AccessFailedCount], [u].[ConcurrencyStamp], [u].[CreatedAt], [u].[Department], [u].[Email], [u].[EmailConfirmed], [u].[EmployeeId], [u].[FirstName], [u].[IsActive], [u].[LastLoginAt], [u].[LastName], [u].[LockoutEnabled], [u].[LockoutEnd], [u].[NormalizedEmail], [u].[NormalizedUserName], [u].[Notes], [u].[PasswordHash], [u].[PhoneNumber], [u].[PhoneNumberConfirmed], [u].[Role], [u].[SecurityStamp], [u].[TwoFactorEnabled], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[NormalizedEmail] = @__normalizedEmail_0
2025-09-09 15:10:47.239 +05:00 [INF] Executed DbCommand (46ms) [Parameters=[@p0='?' (Size = 450), @p1='?' (Size = 450), @p25='?' (Size = 450), @p2='?' (DbType = Int32), @p3='?' (Size = 4000), @p26='?' (Size = 4000), @p4='?' (DbType = DateTime2), @p5='?' (Size = 200), @p6='?' (Size = 256), @p7='?' (DbType = Boolean), @p8='?' (Size = 15), @p9='?' (Size = 100), @p10='?' (DbType = Boolean), @p11='?' (DbType = DateTime2), @p12='?' (Size = 100), @p13='?' (DbType = Boolean), @p14='?' (DbType = DateTimeOffset), @p15='?' (Size = 256), @p16='?' (Size = 256), @p17='?' (Size = 500), @p18='?' (Size = 4000), @p19='?' (Size = 4000), @p20='?' (DbType = Boolean), @p21='?' (DbType = Int32), @p22='?' (Size = 4000), @p23='?' (DbType = Boolean), @p24='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SET NOCOUNT ON;
INSERT INTO [UserRoles] ([RoleId], [UserId])
VALUES (@p0, @p1);
UPDATE [Users] SET [AccessFailedCount] = @p2, [ConcurrencyStamp] = @p3, [CreatedAt] = @p4, [Department] = @p5, [Email] = @p6, [EmailConfirmed] = @p7, [EmployeeId] = @p8, [FirstName] = @p9, [IsActive] = @p10, [LastLoginAt] = @p11, [LastName] = @p12, [LockoutEnabled] = @p13, [LockoutEnd] = @p14, [NormalizedEmail] = @p15, [NormalizedUserName] = @p16, [Notes] = @p17, [PasswordHash] = @p18, [PhoneNumber] = @p19, [PhoneNumberConfirmed] = @p20, [Role] = @p21, [SecurityStamp] = @p22, [TwoFactorEnabled] = @p23, [UserName] = @p24
OUTPUT 1
WHERE [Id] = @p25 AND [ConcurrencyStamp] = @p26;
2025-09-09 15:10:47.254 +05:00 [INF] Default admin user created successfully
2025-09-09 15:10:47.465 +05:00 [INF] Now listening on: https://localhost:7050
2025-09-09 15:10:47.466 +05:00 [INF] Now listening on: http://localhost:5052
2025-09-09 15:10:47.521 +05:00 [INF] Application started. Press Ctrl+C to shut down.
2025-09-09 15:10:47.526 +05:00 [INF] Hosting environment: Development
2025-09-09 15:10:47.528 +05:00 [INF] Content root path: C:\Users\<USER>\Desktop\CRM\CRM\CRM
2025-09-09 15:10:48.103 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/ - null null
2025-09-09 15:10:48.290 +05:00 [INF] Executing endpoint 'CRM.Controllers.HomeController.Index (CRM)'
2025-09-09 15:10:48.299 +05:00 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller CRM.Controllers.HomeController (CRM).
2025-09-09 15:10:48.311 +05:00 [INF] Executing RedirectResult, redirecting to /Account/Login.
2025-09-09 15:10:48.315 +05:00 [INF] Executed action CRM.Controllers.HomeController.Index (CRM) in 12.1817ms
2025-09-09 15:10:48.316 +05:00 [INF] Executed endpoint 'CRM.Controllers.HomeController.Index (CRM)'
2025-09-09 15:10:48.320 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/ - 302 0 null 225.795ms
2025-09-09 15:10:48.328 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/Account/Login - null null
2025-09-09 15:10:48.337 +05:00 [INF] Executing endpoint 'CRM.Controllers.AccountController.Login (CRM)'
2025-09-09 15:10:48.347 +05:00 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Login(System.String) on controller CRM.Controllers.AccountController (CRM).
2025-09-09 15:10:48.381 +05:00 [INF] Executing ViewResult, running view Login.
2025-09-09 15:10:48.497 +05:00 [INF] Executed ViewResult - view Login executed in 121.2361ms.
2025-09-09 15:10:48.506 +05:00 [INF] Executed action CRM.Controllers.AccountController.Login (CRM) in 155.9489ms
2025-09-09 15:10:48.509 +05:00 [INF] Executed endpoint 'CRM.Controllers.AccountController.Login (CRM)'
2025-09-09 15:10:48.517 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/Account/Login - 200 null text/html; charset=utf-8 188.6305ms
2025-09-09 15:10:48.530 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/lib/bootstrap/dist/css/bootstrap.min.css - null null
2025-09-09 15:10:48.532 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/lib/bootstrap/dist/js/bootstrap.bundle.min.js - null null
2025-09-09 15:10:48.532 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/lib/jquery/dist/jquery.min.js - null null
2025-09-09 15:10:48.531 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/css/site.css?v=pAGv4ietcJNk_EwsQZ5BN9-K4MuNYS2a9wl4Jw-q9D0 - null null
2025-09-09 15:10:48.532 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/CRM.styles.css?v=KYwwMofvENgijtH6pIYpZaCCSl2_TVHZJKD3FUa6xUQ - null null
2025-09-09 15:10:48.534 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/js/site.js?v=hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo - null null
2025-09-09 15:10:48.549 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/_framework/aspnetcore-browser-refresh.js - null null
2025-09-09 15:10:48.545 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/lib/jquery-validation/dist/jquery.validate.min.js - null null
2025-09-09 15:10:48.549 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js - null null
2025-09-09 15:10:48.603 +05:00 [INF] Sending file. Request path: '/lib/bootstrap/dist/css/bootstrap.min.css'. Physical path: 'C:\Users\<USER>\Desktop\CRM\CRM\CRM\wwwroot\lib\bootstrap\dist\css\bootstrap.min.css'
2025-09-09 15:10:48.603 +05:00 [INF] Sending file. Request path: '/js/site.js'. Physical path: 'C:\Users\<USER>\Desktop\CRM\CRM\CRM\wwwroot\js\site.js'
2025-09-09 15:10:48.603 +05:00 [INF] Sending file. Request path: '/lib/jquery/dist/jquery.min.js'. Physical path: 'C:\Users\<USER>\Desktop\CRM\CRM\CRM\wwwroot\lib\jquery\dist\jquery.min.js'
2025-09-09 15:10:48.605 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/_framework/aspnetcore-browser-refresh.js - 200 16491 application/javascript; charset=utf-8 55.4263ms
2025-09-09 15:10:48.603 +05:00 [INF] Sending file. Request path: '/lib/jquery-validation/dist/jquery.validate.min.js'. Physical path: 'C:\Users\<USER>\Desktop\CRM\CRM\CRM\wwwroot\lib\jquery-validation\dist\jquery.validate.min.js'
2025-09-09 15:10:48.603 +05:00 [INF] Sending file. Request path: '/CRM.styles.css'. Physical path: 'C:\Users\<USER>\Desktop\CRM\CRM\CRM\obj\Debug\net8.0\scopedcss\bundle\CRM.styles.css'
2025-09-09 15:10:48.603 +05:00 [INF] Sending file. Request path: '/lib/bootstrap/dist/js/bootstrap.bundle.min.js'. Physical path: 'C:\Users\<USER>\Desktop\CRM\CRM\CRM\wwwroot\lib\bootstrap\dist\js\bootstrap.bundle.min.js'
2025-09-09 15:10:48.603 +05:00 [INF] Sending file. Request path: '/css/site.css'. Physical path: 'C:\Users\<USER>\Desktop\CRM\CRM\CRM\wwwroot\css\site.css'
2025-09-09 15:10:48.608 +05:00 [INF] Sending file. Request path: '/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js'. Physical path: 'C:\Users\<USER>\Desktop\CRM\CRM\CRM\wwwroot\lib\jquery-validation-unobtrusive\jquery.validate.unobtrusive.min.js'
2025-09-09 15:10:48.608 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/lib/bootstrap/dist/css/bootstrap.min.css - 200 162720 text/css 77.7858ms
2025-09-09 15:10:48.610 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/js/site.js?v=hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo - 200 231 text/javascript 77.2372ms
2025-09-09 15:10:48.611 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/lib/jquery/dist/jquery.min.js - 200 89501 text/javascript 78.9459ms
2025-09-09 15:10:48.616 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/lib/jquery-validation/dist/jquery.validate.min.js - 200 24601 text/javascript 72.4702ms
2025-09-09 15:10:48.619 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/CRM.styles.css?v=KYwwMofvENgijtH6pIYpZaCCSl2_TVHZJKD3FUa6xUQ - 200 1121 text/css 87.125ms
2025-09-09 15:10:48.620 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/lib/bootstrap/dist/js/bootstrap.bundle.min.js - 200 78468 text/javascript 87.9536ms
2025-09-09 15:10:48.623 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/css/site.css?v=pAGv4ietcJNk_EwsQZ5BN9-K4MuNYS2a9wl4Jw-q9D0 - 200 362 text/css 91.3685ms
2025-09-09 15:10:48.625 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/_vs/browserLink - null null
2025-09-09 15:10:48.625 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js - 200 5824 text/javascript 75.8757ms
2025-09-09 15:10:48.705 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/_vs/browserLink - 200 null text/javascript; charset=UTF-8 80.9009ms
2025-09-09 15:10:48.722 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/favicon.ico - null null
2025-09-09 15:10:48.730 +05:00 [INF] Sending file. Request path: '/favicon.ico'. Physical path: 'C:\Users\<USER>\Desktop\CRM\CRM\CRM\wwwroot\favicon.ico'
2025-09-09 15:10:48.732 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/favicon.ico - 200 5430 image/x-icon 9.7636ms
2025-09-09 15:11:10.460 +05:00 [INF] Request starting HTTP/2 POST https://localhost:7050/Account/Login - application/x-www-form-urlencoded 253
2025-09-09 15:11:10.479 +05:00 [INF] Executing endpoint 'CRM.Controllers.AccountController.Login (CRM)'
2025-09-09 15:11:10.486 +05:00 [INF] Route matched with {action = "Login", controller = "Account"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Login(CRM.Models.ViewModels.LoginViewModel) on controller CRM.Controllers.AccountController (CRM).
2025-09-09 15:11:10.531 +05:00 [INF] Executed DbCommand (11ms) [Parameters=[@__normalizedEmail_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [u].[Id], [u].[AccessFailedCount], [u].[ConcurrencyStamp], [u].[CreatedAt], [u].[Department], [u].[Email], [u].[EmailConfirmed], [u].[EmployeeId], [u].[FirstName], [u].[IsActive], [u].[LastLoginAt], [u].[LastName], [u].[LockoutEnabled], [u].[LockoutEnd], [u].[NormalizedEmail], [u].[NormalizedUserName], [u].[Notes], [u].[PasswordHash], [u].[PhoneNumber], [u].[PhoneNumberConfirmed], [u].[Role], [u].[SecurityStamp], [u].[TwoFactorEnabled], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[NormalizedEmail] = @__normalizedEmail_0
2025-09-09 15:11:10.540 +05:00 [INF] Executed DbCommand (4ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[AccessFailedCount], [u].[ConcurrencyStamp], [u].[CreatedAt], [u].[Department], [u].[Email], [u].[EmailConfirmed], [u].[EmployeeId], [u].[FirstName], [u].[IsActive], [u].[LastLoginAt], [u].[LastName], [u].[LockoutEnabled], [u].[LockoutEnd], [u].[NormalizedEmail], [u].[NormalizedUserName], [u].[Notes], [u].[PasswordHash], [u].[PhoneNumber], [u].[PhoneNumberConfirmed], [u].[Role], [u].[SecurityStamp], [u].[TwoFactorEnabled], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[NormalizedUserName] = @__normalizedUserName_0
2025-09-09 15:11:10.616 +05:00 [INF] Executed DbCommand (5ms) [Parameters=[@__user_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [u].[Id], [u].[ClaimType], [u].[ClaimValue], [u].[UserId]
FROM [UserClaims] AS [u]
WHERE [u].[UserId] = @__user_Id_0
2025-09-09 15:11:10.647 +05:00 [INF] Executed DbCommand (15ms) [Parameters=[@__userId_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[Name]
FROM [UserRoles] AS [u]
INNER JOIN [Roles] AS [r] ON [u].[RoleId] = [r].[Id]
WHERE [u].[UserId] = @__userId_0
2025-09-09 15:11:10.655 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[@__normalizedName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [r].[Id], [r].[ConcurrencyStamp], [r].[Name], [r].[NormalizedName]
FROM [Roles] AS [r]
WHERE [r].[NormalizedName] = @__normalizedName_0
2025-09-09 15:11:10.671 +05:00 [INF] Executed DbCommand (4ms) [Parameters=[@__role_Id_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT [r].[ClaimType], [r].[ClaimValue]
FROM [RoleClaims] AS [r]
WHERE [r].[RoleId] = @__role_Id_0
2025-09-09 15:11:10.677 +05:00 [INF] AuthenticationScheme: Identity.Application signed in.
2025-09-09 15:11:10.681 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedUserName_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[AccessFailedCount], [u].[ConcurrencyStamp], [u].[CreatedAt], [u].[Department], [u].[Email], [u].[EmailConfirmed], [u].[EmployeeId], [u].[FirstName], [u].[IsActive], [u].[LastLoginAt], [u].[LastName], [u].[LockoutEnabled], [u].[LockoutEnd], [u].[NormalizedEmail], [u].[NormalizedUserName], [u].[Notes], [u].[PasswordHash], [u].[PhoneNumber], [u].[PhoneNumberConfirmed], [u].[Role], [u].[SecurityStamp], [u].[TwoFactorEnabled], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[NormalizedUserName] = @__normalizedUserName_0
2025-09-09 15:11:10.684 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[@__normalizedEmail_0='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(2) [u].[Id], [u].[AccessFailedCount], [u].[ConcurrencyStamp], [u].[CreatedAt], [u].[Department], [u].[Email], [u].[EmailConfirmed], [u].[EmployeeId], [u].[FirstName], [u].[IsActive], [u].[LastLoginAt], [u].[LastName], [u].[LockoutEnabled], [u].[LockoutEnd], [u].[NormalizedEmail], [u].[NormalizedUserName], [u].[Notes], [u].[PasswordHash], [u].[PhoneNumber], [u].[PhoneNumberConfirmed], [u].[Role], [u].[SecurityStamp], [u].[TwoFactorEnabled], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[NormalizedEmail] = @__normalizedEmail_0
2025-09-09 15:11:10.697 +05:00 [INF] Executed DbCommand (11ms) [Parameters=[@p23='?' (Size = 450), @p0='?' (DbType = Int32), @p1='?' (Size = 4000), @p24='?' (Size = 4000), @p2='?' (DbType = DateTime2), @p3='?' (Size = 200), @p4='?' (Size = 256), @p5='?' (DbType = Boolean), @p6='?' (Size = 15), @p7='?' (Size = 100), @p8='?' (DbType = Boolean), @p9='?' (DbType = DateTime2), @p10='?' (Size = 100), @p11='?' (DbType = Boolean), @p12='?' (DbType = DateTimeOffset), @p13='?' (Size = 256), @p14='?' (Size = 256), @p15='?' (Size = 500), @p16='?' (Size = 4000), @p17='?' (Size = 4000), @p18='?' (DbType = Boolean), @p19='?' (DbType = Int32), @p20='?' (Size = 4000), @p21='?' (DbType = Boolean), @p22='?' (Size = 256)], CommandType='"Text"', CommandTimeout='30']
SET IMPLICIT_TRANSACTIONS OFF;
SET NOCOUNT ON;
UPDATE [Users] SET [AccessFailedCount] = @p0, [ConcurrencyStamp] = @p1, [CreatedAt] = @p2, [Department] = @p3, [Email] = @p4, [EmailConfirmed] = @p5, [EmployeeId] = @p6, [FirstName] = @p7, [IsActive] = @p8, [LastLoginAt] = @p9, [LastName] = @p10, [LockoutEnabled] = @p11, [LockoutEnd] = @p12, [NormalizedEmail] = @p13, [NormalizedUserName] = @p14, [Notes] = @p15, [PasswordHash] = @p16, [PhoneNumber] = @p17, [PhoneNumberConfirmed] = @p18, [Role] = @p19, [SecurityStamp] = @p20, [TwoFactorEnabled] = @p21, [UserName] = @p22
OUTPUT 1
WHERE [Id] = @p23 AND [ConcurrencyStamp] = @p24;
2025-09-09 15:11:10.700 +05:00 [INF] User <EMAIL> logged in successfully
2025-09-09 15:11:10.701 +05:00 [INF] User <EMAIL> logged in successfully
2025-09-09 15:11:10.704 +05:00 [INF] Executing RedirectResult, redirecting to /Dashboard.
2025-09-09 15:11:10.707 +05:00 [INF] Executed action CRM.Controllers.AccountController.Login (CRM) in 217.2582ms
2025-09-09 15:11:10.709 +05:00 [INF] Executed endpoint 'CRM.Controllers.AccountController.Login (CRM)'
2025-09-09 15:11:10.712 +05:00 [INF] Request finished HTTP/2 POST https://localhost:7050/Account/Login - 302 0 null 251.6065ms
2025-09-09 15:11:10.714 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/Dashboard - null null
2025-09-09 15:11:10.764 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[AccessFailedCount], [u].[ConcurrencyStamp], [u].[CreatedAt], [u].[Department], [u].[Email], [u].[EmailConfirmed], [u].[EmployeeId], [u].[FirstName], [u].[IsActive], [u].[LastLoginAt], [u].[LastName], [u].[LockoutEnabled], [u].[LockoutEnd], [u].[NormalizedEmail], [u].[NormalizedUserName], [u].[Notes], [u].[PasswordHash], [u].[PhoneNumber], [u].[PhoneNumberConfirmed], [u].[Role], [u].[SecurityStamp], [u].[TwoFactorEnabled], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-09-09 15:11:10.766 +05:00 [INF] Executing endpoint 'CRM.Controllers.DashboardController.Index (CRM)'
2025-09-09 15:11:10.769 +05:00 [INF] Route matched with {action = "Index", controller = "Dashboard"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller CRM.Controllers.DashboardController (CRM).
2025-09-09 15:11:10.796 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Sales] AS [s]
WHERE [s].[IsActive] = CAST(1 AS bit)
2025-09-09 15:11:10.811 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[@__startOfMonth_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM([s].[FinalAmount]), 0.0)
FROM [Sales] AS [s]
WHERE [s].[IsActive] = CAST(1 AS bit) AND [s].[SaleDate] >= @__startOfMonth_0
2025-09-09 15:11:10.820 +05:00 [INF] Executed DbCommand (3ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Customers] AS [c]
WHERE [c].[IsActive] = CAST(1 AS bit)
2025-09-09 15:11:10.835 +05:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Queries] AS [q]
WHERE [q].[Status] IN (1, 2)
2025-09-09 15:11:10.845 +05:00 [INF] Executed DbCommand (4ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Jobs] AS [j]
WHERE [j].[Status] IN (1, 2)
2025-09-09 15:11:10.881 +05:00 [INF] Executed DbCommand (12ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM (
    SELECT [i].[DeviceId], [i].[SIMCardId]
    FROM [InventoryItems] AS [i]
    GROUP BY [i].[DeviceId], [i].[SIMCardId]
    HAVING COUNT(CASE
        WHEN [i].[Status] = 1 THEN 1
    END) < 10
) AS [t]
2025-09-09 15:11:10.902 +05:00 [INF] Executed DbCommand (8ms) [Parameters=[@__AddDays_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Sales] AS [s]
WHERE [s].[IsActive] = CAST(1 AS bit) AND [s].[RenewalDate] <= @__AddDays_0
2025-09-09 15:11:10.910 +05:00 [INF] Executing ViewResult, running view ManagementDashboard.
2025-09-09 15:11:10.913 +05:00 [ERR] The view 'ManagementDashboard' was not found. Searched locations: ["/Views/Dashboard/ManagementDashboard.cshtml","/Views/Shared/ManagementDashboard.cshtml"]
2025-09-09 15:11:10.920 +05:00 [INF] Executed action CRM.Controllers.DashboardController.Index (CRM) in 148.9319ms
2025-09-09 15:11:10.923 +05:00 [INF] Executed endpoint 'CRM.Controllers.DashboardController.Index (CRM)'
2025-09-09 15:11:10.987 +05:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The view 'ManagementDashboard' was not found. The following locations were searched:
/Views/Dashboard/ManagementDashboard.cshtml
/Views/Shared/ManagementDashboard.cshtml
   at Microsoft.AspNetCore.Mvc.ViewEngines.ViewEngineResult.EnsureSuccessful(IEnumerable`1 originalLocations)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Program.<>c.<<<Main>$>b__0_4>d.MoveNext() in C:\Users\<USER>\Desktop\CRM\CRM\CRM\Program.cs:line 127
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-09-09 15:11:11.050 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/Dashboard - 500 null text/html; charset=utf-8 335.9788ms
2025-09-09 15:11:11.085 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/_vs/browserLink - null null
2025-09-09 15:11:11.095 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/_framework/aspnetcore-browser-refresh.js - null null
2025-09-09 15:11:11.102 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/_framework/aspnetcore-browser-refresh.js - 200 16491 application/javascript; charset=utf-8 8.0472ms
2025-09-09 15:11:11.114 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/_vs/browserLink - 200 null text/javascript; charset=UTF-8 29.8127ms
2025-09-09 15:11:37.643 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/ - null null
2025-09-09 15:11:48.879 +05:00 [INF] Executed DbCommand (11,171ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[AccessFailedCount], [u].[ConcurrencyStamp], [u].[CreatedAt], [u].[Department], [u].[Email], [u].[EmailConfirmed], [u].[EmployeeId], [u].[FirstName], [u].[IsActive], [u].[LastLoginAt], [u].[LastName], [u].[LockoutEnabled], [u].[LockoutEnd], [u].[NormalizedEmail], [u].[NormalizedUserName], [u].[Notes], [u].[PasswordHash], [u].[PhoneNumber], [u].[PhoneNumberConfirmed], [u].[Role], [u].[SecurityStamp], [u].[TwoFactorEnabled], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-09-09 15:11:48.882 +05:00 [INF] Executing endpoint 'CRM.Controllers.HomeController.Index (CRM)'
2025-09-09 15:11:48.883 +05:00 [INF] Route matched with {action = "Index", controller = "Home"}. Executing controller action with signature Microsoft.AspNetCore.Mvc.IActionResult Index() on controller CRM.Controllers.HomeController (CRM).
2025-09-09 15:11:48.885 +05:00 [INF] Executing RedirectResult, redirecting to /Dashboard.
2025-09-09 15:11:48.887 +05:00 [INF] Executed action CRM.Controllers.HomeController.Index (CRM) in 1.5249ms
2025-09-09 15:11:48.889 +05:00 [INF] Executed endpoint 'CRM.Controllers.HomeController.Index (CRM)'
2025-09-09 15:11:48.890 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/ - 302 0 null 11247.4987ms
2025-09-09 15:11:48.896 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/Dashboard - null null
2025-09-09 15:11:48.920 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[@__p_0='?' (Size = 450)], CommandType='"Text"', CommandTimeout='30']
SELECT TOP(1) [u].[Id], [u].[AccessFailedCount], [u].[ConcurrencyStamp], [u].[CreatedAt], [u].[Department], [u].[Email], [u].[EmailConfirmed], [u].[EmployeeId], [u].[FirstName], [u].[IsActive], [u].[LastLoginAt], [u].[LastName], [u].[LockoutEnabled], [u].[LockoutEnd], [u].[NormalizedEmail], [u].[NormalizedUserName], [u].[Notes], [u].[PasswordHash], [u].[PhoneNumber], [u].[PhoneNumberConfirmed], [u].[Role], [u].[SecurityStamp], [u].[TwoFactorEnabled], [u].[UserName]
FROM [Users] AS [u]
WHERE [u].[Id] = @__p_0
2025-09-09 15:11:48.924 +05:00 [INF] Executing endpoint 'CRM.Controllers.DashboardController.Index (CRM)'
2025-09-09 15:11:48.926 +05:00 [INF] Route matched with {action = "Index", controller = "Dashboard"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] Index() on controller CRM.Controllers.DashboardController (CRM).
2025-09-09 15:11:53.922 +05:00 [INF] Executed DbCommand (4,993ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Sales] AS [s]
WHERE [s].[IsActive] = CAST(1 AS bit)
2025-09-09 15:11:53.937 +05:00 [INF] Executed DbCommand (4ms) [Parameters=[@__startOfMonth_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT COALESCE(SUM([s].[FinalAmount]), 0.0)
FROM [Sales] AS [s]
WHERE [s].[IsActive] = CAST(1 AS bit) AND [s].[SaleDate] >= @__startOfMonth_0
2025-09-09 15:11:53.954 +05:00 [INF] Executed DbCommand (8ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Customers] AS [c]
WHERE [c].[IsActive] = CAST(1 AS bit)
2025-09-09 15:11:53.969 +05:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Queries] AS [q]
WHERE [q].[Status] IN (1, 2)
2025-09-09 15:11:53.983 +05:00 [INF] Executed DbCommand (6ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Jobs] AS [j]
WHERE [j].[Status] IN (1, 2)
2025-09-09 15:11:54.002 +05:00 [INF] Executed DbCommand (10ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM (
    SELECT [i].[DeviceId], [i].[SIMCardId]
    FROM [InventoryItems] AS [i]
    GROUP BY [i].[DeviceId], [i].[SIMCardId]
    HAVING COUNT(CASE
        WHEN [i].[Status] = 1 THEN 1
    END) < 10
) AS [t]
2025-09-09 15:11:54.023 +05:00 [INF] Executed DbCommand (7ms) [Parameters=[@__AddDays_0='?' (DbType = DateTime2)], CommandType='"Text"', CommandTimeout='30']
SELECT COUNT(*)
FROM [Sales] AS [s]
WHERE [s].[IsActive] = CAST(1 AS bit) AND [s].[RenewalDate] <= @__AddDays_0
2025-09-09 15:11:54.034 +05:00 [INF] Executing ViewResult, running view ManagementDashboard.
2025-09-09 15:11:54.036 +05:00 [ERR] The view 'ManagementDashboard' was not found. Searched locations: ["/Views/Dashboard/ManagementDashboard.cshtml","/Views/Shared/ManagementDashboard.cshtml"]
2025-09-09 15:11:54.044 +05:00 [INF] Executed action CRM.Controllers.DashboardController.Index (CRM) in 5115.6479ms
2025-09-09 15:11:54.047 +05:00 [INF] Executed endpoint 'CRM.Controllers.DashboardController.Index (CRM)'
2025-09-09 15:11:54.142 +05:00 [ERR] An unhandled exception has occurred while executing the request.
System.InvalidOperationException: The view 'ManagementDashboard' was not found. The following locations were searched:
/Views/Dashboard/ManagementDashboard.cshtml
/Views/Shared/ManagementDashboard.cshtml
   at Microsoft.AspNetCore.Mvc.ViewEngines.ViewEngineResult.EnsureSuccessful(IEnumerable`1 originalLocations)
   at Microsoft.AspNetCore.Mvc.ViewFeatures.ViewResultExecutor.ExecuteAsync(ActionContext context, ViewResult result)
   at Microsoft.AspNetCore.Mvc.ViewResult.ExecuteResultAsync(ActionContext context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResultFilterAsync>g__Awaited|30_0[TFilter,TFilterAsync](ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResultExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.ResultNext[TFilter,TFilterAsync](State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.InvokeResultFilters()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeNextResourceFilter>g__Awaited|25_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Rethrow(ResourceExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Logged|17_1(ResourceInvoker invoker)
   at Microsoft.AspNetCore.Routing.EndpointMiddleware.<Invoke>g__AwaitRequestTask|7_0(Endpoint endpoint, Task requestTask, ILogger logger)
   at Program.<>c.<<<Main>$>b__0_4>d.MoveNext() in C:\Users\<USER>\Desktop\CRM\CRM\CRM\Program.cs:line 127
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.Diagnostics.DeveloperExceptionPageMiddlewareImpl.Invoke(HttpContext context)
2025-09-09 15:11:54.166 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/Dashboard - 500 null text/html; charset=utf-8 5269.9452ms
2025-09-09 15:11:54.209 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/_vs/browserLink - null null
2025-09-09 15:11:54.210 +05:00 [INF] Request starting HTTP/2 GET https://localhost:7050/_framework/aspnetcore-browser-refresh.js - null null
2025-09-09 15:11:54.248 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/_framework/aspnetcore-browser-refresh.js - 200 16491 application/javascript; charset=utf-8 37.354ms
2025-09-09 15:11:54.270 +05:00 [INF] Request finished HTTP/2 GET https://localhost:7050/_vs/browserLink - 200 null text/javascript; charset=UTF-8 61.3371ms
2025-09-09 15:13:54.316 +05:00 [WRN] The foreign key property 'StockTransfer.InventoryItemId1' was created in shadow state because a conflicting property with the simple name 'InventoryItemId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-09-09 15:13:54.510 +05:00 [WRN] The foreign key property 'StockTransfer.InventoryItemId1' was created in shadow state because a conflicting property with the simple name 'InventoryItemId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-09-09 15:14:07.073 +05:00 [WRN] The foreign key property 'StockTransfer.InventoryItemId1' was created in shadow state because a conflicting property with the simple name 'InventoryItemId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-09-09 15:14:07.310 +05:00 [INF] Executed DbCommand (7ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-09-09 15:14:07.330 +05:00 [INF] Executed DbCommand (14ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-09-09 15:14:07.332 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-09-09 15:14:07.332 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-09-09 15:14:07.343 +05:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
2025-09-09 15:14:07.350 +05:00 [INF] Applying migration '20250909101354_CRM'.
2025-09-09 15:14:07.412 +05:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE [Devices] SET [CreatedAt] = '2025-09-09T10:13:54.4884920Z', [UpdatedAt] = '2025-09-09T10:13:54.4884921Z'
WHERE [DeviceId] = 1;
SELECT @@ROWCOUNT;
2025-09-09 15:14:07.413 +05:00 [INF] Executed DbCommand (1ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE [Devices] SET [CreatedAt] = '2025-09-09T10:13:54.4884923Z', [UpdatedAt] = '2025-09-09T10:13:54.4884923Z'
WHERE [DeviceId] = 2;
SELECT @@ROWCOUNT;
2025-09-09 15:14:07.450 +05:00 [INF] Executed DbCommand (37ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE [SIMCards] SET [CreatedAt] = '2025-09-09T10:13:54.4884945Z', [UpdatedAt] = '2025-09-09T10:13:54.4884946Z'
WHERE [SIMCardId] = 1;
SELECT @@ROWCOUNT;
2025-09-09 15:14:07.467 +05:00 [INF] Executed DbCommand (16ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
UPDATE [SIMCards] SET [CreatedAt] = '2025-09-09T10:13:54.4884948Z', [UpdatedAt] = '2025-09-09T10:13:54.4884948Z'
WHERE [SIMCardId] = 2;
SELECT @@ROWCOUNT;
2025-09-09 15:14:07.485 +05:00 [INF] Executed DbCommand (18ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250909101354_CRM', N'8.0.0');
2025-09-09 15:17:27.085 +05:00 [WRN] The foreign key property 'StockTransfer.InventoryItemId1' was created in shadow state because a conflicting property with the simple name 'InventoryItemId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-09-09 15:17:27.253 +05:00 [WRN] The foreign key property 'StockTransfer.InventoryItemId1' was created in shadow state because a conflicting property with the simple name 'InventoryItemId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core.
2025-09-09 15:17:27.639 +05:00 [INF] Executed DbCommand (9ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT 1
2025-09-09 15:17:27.664 +05:00 [INF] Executed DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT OBJECT_ID(N'[__EFMigrationsHistory]');
2025-09-09 15:17:27.672 +05:00 [INF] Executed DbCommand (5ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT [MigrationId], [ProductVersion]
FROM [__EFMigrationsHistory]
ORDER BY [MigrationId];
