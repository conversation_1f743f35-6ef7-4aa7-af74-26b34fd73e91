using System.ComponentModel.DataAnnotations;

namespace CRM.Models
{
    public enum UserRole
    {
        [Display(Name = "Salesperson")]
        Salesperson = 1,
        
        [Display(Name = "Operations Staff")]
        OperationsStaff = 2,
        
        [Display(Name = "Technician")]
        Technician = 3,
        
        [Display(Name = "Management")]
        Management = 4
    }

    public enum QueryStatus
    {
        [Display(Name = "Pending")]
        Pending = 1,
        
        [Display(Name = "Under Review")]
        UnderReview = 2,
        
        [Display(Name = "Approved")]
        Approved = 3,
        
        [Display(Name = "Rejected")]
        Rejected = 4,
        
        [Display(Name = "Modification Required")]
        ModificationRequired = 5
    }

    public enum JobStatus
    {
        [Display(Name = "Scheduled")]
        Scheduled = 1,
        
        [Display(Name = "In Progress")]
        InProgress = 2,
        
        [Display(Name = "Completed")]
        Completed = 3,
        
        [Display(Name = "Issues Encountered")]
        IssuesEncountered = 4,
        
        [Display(Name = "Cancelled")]
        Cancelled = 5
    }

    public enum InventoryStatus
    {
        [Display(Name = "Available")]
        Available = 1,
        
        [Display(Name = "Assigned")]
        Assigned = 2,
        
        [Display(Name = "Returned")]
        Returned = 3,
        
        [Display(Name = "Dead Stock")]
        DeadStock = 4
    }

    public enum DeadStockCategory
    {
        [Display(Name = "Damaged")]
        Damaged = 1,
        
        [Display(Name = "Obsolete")]
        Obsolete = 2,
        
        [Display(Name = "Lost")]
        Lost = 3,
        
        [Display(Name = "Defective")]
        Defective = 4
    }

    public enum DeviceType
    {
        [Display(Name = "GPS Tracker")]
        GPSTracker = 1,
        
        [Display(Name = "Security Camera")]
        SecurityCamera = 2,
        
        [Display(Name = "IoT Sensor")]
        IoTSensor = 3,
        
        [Display(Name = "Communication Device")]
        CommunicationDevice = 4
    }

    public enum NotificationType
    {
        [Display(Name = "Renewal Alert")]
        RenewalAlert = 1,
        
        [Display(Name = "Inventory Alert")]
        InventoryAlert = 2,
        
        [Display(Name = "Operational Notification")]
        OperationalNotification = 3,
        
        [Display(Name = "Dead Stock Report")]
        DeadStockReport = 4
    }

    public enum Priority
    {
        [Display(Name = "Low")]
        Low = 1,
        
        [Display(Name = "Medium")]
        Medium = 2,
        
        [Display(Name = "High")]
        High = 3,
        
        [Display(Name = "Critical")]
        Critical = 4
    }

    public enum TransferReason
    {
        [Display(Name = "Assignment")]
        Assignment = 1,
        
        [Display(Name = "Return")]
        Return = 2,
        
        [Display(Name = "Reallocation")]
        Reallocation = 3,
        
        [Display(Name = "Maintenance")]
        Maintenance = 4,
        
        [Display(Name = "Customer Return")]
        CustomerReturn = 5
    }
}
