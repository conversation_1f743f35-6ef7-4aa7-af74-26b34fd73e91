using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CRM.Models
{
    public class Query
    {
        [Key]
        public int QueryId { get; set; }

        [Required]
        [StringLength(20)]
        public string QueryNumber { get; set; } = string.Empty;

        [Required]
        public int SaleId { get; set; }

        [Required]
        public string CreatedByUserId { get; set; } = string.Empty;

        public string? ProcessedByUserId { get; set; }

        [Required]
        public QueryStatus Status { get; set; } = QueryStatus.Pending;

        [Required]
        public Priority Priority { get; set; } = Priority.Medium;

        [Required]
        [StringLength(200)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        [StringLength(2000)]
        public string Description { get; set; } = string.Empty;

        [StringLength(2000)]
        public string? Response { get; set; }

        [StringLength(2000)]
        public string? InternalNotes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? ProcessedAt { get; set; }

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? DueDate { get; set; }

        // Foreign Keys
        [ForeignKey("SaleId")]
        public virtual Sale Sale { get; set; } = null!;

        [ForeignKey("CreatedByUserId")]
        public virtual ApplicationUser CreatedByUser { get; set; } = null!;

        [ForeignKey("ProcessedByUserId")]
        public virtual ApplicationUser? ProcessedByUser { get; set; }

        // Navigation properties
        public virtual ICollection<QueryComment> Comments { get; set; } = new List<QueryComment>();
        public virtual ICollection<QueryAttachment> Attachments { get; set; } = new List<QueryAttachment>();

        [NotMapped]
        public string DisplayName => $"Query #{QueryNumber}";

        [NotMapped]
        public bool IsOverdue => DueDate.HasValue && DueDate.Value < DateTime.UtcNow && Status != QueryStatus.Approved && Status != QueryStatus.Rejected;

        [NotMapped]
        public TimeSpan? ProcessingTime => ProcessedAt?.Subtract(CreatedAt);
    }

    public class QueryComment
    {
        [Key]
        public int CommentId { get; set; }

        [Required]
        public int QueryId { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        [StringLength(2000)]
        public string Comment { get; set; } = string.Empty;

        public bool IsInternal { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Foreign Keys
        [ForeignKey("QueryId")]
        public virtual Query Query { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; } = null!;
    }

    public class QueryAttachment
    {
        [Key]
        public int AttachmentId { get; set; }

        [Required]
        public int QueryId { get; set; }

        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [StringLength(100)]
        public string? ContentType { get; set; }

        public long FileSize { get; set; }

        [Required]
        public string UploadedByUserId { get; set; } = string.Empty;

        public DateTime UploadedAt { get; set; } = DateTime.UtcNow;

        // Foreign Keys
        [ForeignKey("QueryId")]
        public virtual Query Query { get; set; } = null!;

        [ForeignKey("UploadedByUserId")]
        public virtual ApplicationUser UploadedByUser { get; set; } = null!;

        [NotMapped]
        public string FileSizeFormatted => FileSize < 1024 ? $"{FileSize} B" :
                                         FileSize < 1024 * 1024 ? $"{FileSize / 1024:F1} KB" :
                                         $"{FileSize / (1024 * 1024):F1} MB";
    }
}
