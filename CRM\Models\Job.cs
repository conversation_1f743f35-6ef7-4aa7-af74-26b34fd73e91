using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CRM.Models
{
    public class Job
    {
        [Key]
        public int JobId { get; set; }

        [Required]
        [StringLength(20)]
        public string JobNumber { get; set; } = string.Empty;

        [Required]
        public int SaleId { get; set; }

        [Required]
        public string AssignedToUserId { get; set; } = string.Empty;

        [Required]
        public JobStatus Status { get; set; } = JobStatus.Scheduled;

        [Required]
        public Priority Priority { get; set; } = Priority.Medium;

        [Required]
        [StringLength(200)]
        public string Title { get; set; } = string.Empty;

        [StringLength(2000)]
        public string? Description { get; set; }

        [StringLength(2000)]
        public string? InstallationInstructions { get; set; }

        public DateTime ScheduledDate { get; set; }

        public DateTime? StartedAt { get; set; }

        public DateTime? CompletedAt { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [StringLength(2000)]
        public string? CompletionNotes { get; set; }

        [StringLength(2000)]
        public string? IssuesEncountered { get; set; }

        // Foreign Keys
        [ForeignKey("SaleId")]
        public virtual Sale Sale { get; set; } = null!;

        [ForeignKey("AssignedToUserId")]
        public virtual ApplicationUser AssignedToUser { get; set; } = null!;

        // Navigation properties
        public virtual ICollection<JobPhoto> Photos { get; set; } = new List<JobPhoto>();
        public virtual ICollection<JobUpdate> Updates { get; set; } = new List<JobUpdate>();

        [NotMapped]
        public string DisplayName => $"Job #{JobNumber}";

        [NotMapped]
        public bool IsOverdue => ScheduledDate < DateTime.UtcNow && Status != JobStatus.Completed && Status != JobStatus.Cancelled;

        [NotMapped]
        public TimeSpan? Duration => CompletedAt?.Subtract(StartedAt ?? ScheduledDate);

        [NotMapped]
        public string CustomerName => Sale?.Customer?.FullName ?? "Unknown Customer";

        [NotMapped]
        public string InstallationAddress => Sale?.Customer?.FullInstallationAddress ?? "Unknown Address";
    }

    public class JobPhoto
    {
        [Key]
        public int PhotoId { get; set; }

        [Required]
        public int JobId { get; set; }

        [Required]
        [StringLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [StringLength(500)]
        public string FilePath { get; set; } = string.Empty;

        [StringLength(500)]
        public string? Caption { get; set; }

        [StringLength(100)]
        public string? PhotoType { get; set; } // Before, During, After, Issue, etc.

        public DateTime TakenAt { get; set; } = DateTime.UtcNow;

        [Required]
        public string UploadedByUserId { get; set; } = string.Empty;

        // Foreign Keys
        [ForeignKey("JobId")]
        public virtual Job Job { get; set; } = null!;

        [ForeignKey("UploadedByUserId")]
        public virtual ApplicationUser UploadedByUser { get; set; } = null!;
    }

    public class JobUpdate
    {
        [Key]
        public int UpdateId { get; set; }

        [Required]
        public int JobId { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [Required]
        public JobStatus PreviousStatus { get; set; }

        [Required]
        public JobStatus NewStatus { get; set; }

        [Required]
        [StringLength(1000)]
        public string UpdateNotes { get; set; } = string.Empty;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Foreign Keys
        [ForeignKey("JobId")]
        public virtual Job Job { get; set; } = null!;

        [ForeignKey("UserId")]
        public virtual ApplicationUser User { get; set; } = null!;

        [NotMapped]
        public string StatusChange => $"{PreviousStatus} → {NewStatus}";
    }
}
